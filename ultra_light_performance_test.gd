extends Node
class_name UltraLightPerformanceTest

# Performance testing for ultra-lightweight knight system
# Designed to test 50+ knights with smooth DOOM state performance

@export var test_duration: float = 60.0
@export var target_knight_count: int = 50  # Test with 50 knights
@export var spawn_radius: float = 30.0
@export var knight_scene: PackedScene  # Assign ultra-light knight scene
@export var trigger_doom_at: float = 20.0  # Trigger DOOM mode earlier

var test_timer: float = 0.0
var knights: Array[UltraLightKnight] = []
var performance_data: Array[Dictionary] = []
var player: Node3D
var manager: UltraLightKnightManager
var doom_triggered: bool = false

func _ready():
	print("=== ULTRA-LIGHT KNIGHT PERFORMANCE TEST ===")
	print("[Ultra Test] Target: %d knights for %d seconds" % [target_knight_count, test_duration])
	
	# Find player
	player = get_tree().get_first_node_in_group("player")
	if not player:
		print("[Ultra Test] ERROR: No player found")
		return
	
	# Create manager if it doesn't exist
	manager = UltraLightKnightManager.get_instance()
	if not manager:
		manager = UltraLightKnightManager.new()
		manager.name = "UltraLightKnightManager"
		get_tree().current_scene.add_child.call_deferred(manager)
		print("[Ultra Test] Created knight manager")
	
	if not knight_scene:
		print("[Ultra Test] ERROR: Knight scene not assigned")
		return

	# Wait a frame for manager to be ready, then spawn knights
	await get_tree().process_frame
	spawn_ultra_light_knights()

func spawn_ultra_light_knights():
	print("[Ultra Test] Spawning %d ultra-light knights..." % target_knight_count)
	
	for i in range(target_knight_count):
		var knight = knight_scene.instantiate() as UltraLightKnight
		if not knight:
			print("[Ultra Test] ERROR: Failed to instantiate knight %d" % i)
			continue
		
		# Random position around player
		var angle = randf() * TAU
		var distance = randf_range(8.0, spawn_radius)
		var spawn_pos = player.global_position + Vector3(
			cos(angle) * distance,
			1.0,
			sin(angle) * distance
		)
		
		knight.global_position = spawn_pos
		get_tree().current_scene.add_child.call_deferred(knight)
		knights.append(knight)
	
	print("[Ultra Test] Queued %d knights for spawning" % knights.size())

func _process(delta):
	test_timer += delta
	
	# Trigger DOOM mode
	if not doom_triggered and test_timer >= trigger_doom_at:
		trigger_doom_mode()
		doom_triggered = true
	
	# Collect data every second
	if int(test_timer) != int(test_timer - delta):
		collect_performance_data()
	
	# End test
	if test_timer >= test_duration:
		end_performance_test()
		set_process(false)

func trigger_doom_mode():
	print("[Ultra Test] *** TRIGGERING DOOM MODE ***")
	if manager:
		manager.activate_doom_mode()
	print("[Ultra Test] All knights now in relentless pursuit!")

func collect_performance_data():
	var frame_data = {
		"timestamp": test_timer,
		"fps": Engine.get_frames_per_second(),
		"knight_count": knights.size(),
		"manager_stats": {}
	}
	
	if manager:
		frame_data.manager_stats = manager.get_performance_stats()
	
	var valid_knights = 0
	var doom_knights = 0
	
	for knight in knights:
		if is_instance_valid(knight):
			valid_knights += 1
			if knight.is_doom_state:
				doom_knights += 1
	
	frame_data["valid_knights"] = valid_knights
	frame_data["doom_knights"] = doom_knights
	
	performance_data.append(frame_data)
	
	# Real-time output with player position
	print("[Ultra Test] T:%.1fs FPS:%.1f Knights:%d/%d DOOM:%d Batch:%d Player:(%s)" % [
		test_timer,
		frame_data.fps,
		valid_knights,
		knights.size(),
		doom_knights,
		frame_data.manager_stats.get("current_batch_index", 0),
		str(player.global_position) if player else "None"
	])

func end_performance_test():
	print("\n=== ULTRA-LIGHT PERFORMANCE TEST RESULTS ===")
	generate_performance_report()
	cleanup_knights()

func generate_performance_report():
	if performance_data.is_empty():
		print("No performance data collected")
		return
	
	var total_fps = 0.0
	var min_fps = 999.0
	var max_fps = 0.0
	var doom_phase_fps = 0.0
	var doom_samples = 0
	
	for data in performance_data:
		var fps = data.fps
		total_fps += fps
		min_fps = min(min_fps, fps)
		max_fps = max(max_fps, fps)
		
		# Track DOOM phase performance
		if data.timestamp >= trigger_doom_at:
			doom_phase_fps += fps
			doom_samples += 1
	
	var avg_fps = total_fps / performance_data.size()
	var doom_avg_fps = doom_phase_fps / max(1, doom_samples)
	
	print("Test Duration: %.1f seconds" % test_duration)
	print("Target Knights: %d" % target_knight_count)
	print("Average FPS: %.1f" % avg_fps)
	print("Minimum FPS: %.1f" % min_fps)
	print("Maximum FPS: %.1f" % max_fps)
	print("DOOM Phase Average FPS: %.1f" % doom_avg_fps)
	
	# Performance assessment
	var performance_grade = "EXCELLENT"
	if avg_fps < 45:
		performance_grade = "POOR"
	elif avg_fps < 55:
		performance_grade = "FAIR"
	elif avg_fps < 58:
		performance_grade = "GOOD"
	
	print("Performance Grade: %s" % performance_grade)
	
	# Success criteria for 50+ knights
	if avg_fps >= 55 and min_fps >= 30 and doom_avg_fps >= 50:
		print("✓ SUCCESS: Ultra-light system handles %d knights smoothly!" % target_knight_count)
		print("✓ DOOM Performance: %.1f FPS average during relentless pursuit" % doom_avg_fps)
	else:
		print("✗ NEEDS IMPROVEMENT: Performance targets not fully met")
		if doom_avg_fps < 50:
			print("  - DOOM phase performance needs optimization")
	
	print("==========================================")

func cleanup_knights():
	print("[Ultra Test] Cleaning up %d knights..." % knights.size())
	for knight in knights:
		if is_instance_valid(knight):
			knight.queue_free()
	knights.clear()
	
	if manager:
		manager.deactivate_doom_mode()
