[gd_scene load_steps=5 format=3 uid="uid://5nqbasyipupf"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_62fs7"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_gdg2c"]
[ext_resource type="BehaviorTree" uid="uid://bf4r652fv5kwi" path="res://demo/ai/trees/tutorial/tutorial_05_selector.tres" id="3_pm5ep"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialSelector" instance=ExtResource("1_62fs7")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_gdg2c")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_gdg2c")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_gdg2c")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_gdg2c")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_gdg2c")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_gdg2c")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_pm5ep")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
