[gd_scene load_steps=5 format=3 uid="uid://2e4ohaqjaawb"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_afx5l"]
[ext_resource type="Texture2D" uid="uid://usu3j55d6dgc" path="res://demo/assets/agent_melee_nuanced.png" id="2_e51r0"]
[ext_resource type="BehaviorTree" uid="uid://c2u6sljqkim0n" path="res://demo/ai/trees/07_agent_melee_nuanced.tres" id="3_b8kcf"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_tnf02"]

[node name="AgentMeleeNuanced" instance=ExtResource("1_afx5l")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_e51r0")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_e51r0")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_e51r0")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_e51r0")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_e51r0")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_e51r0")

[node name="Health" parent="." index="3"]
max_health = 6.0

[node name="BTPlayer" type="BTPlayer" parent="." index="4"]
behavior_tree = ExtResource("3_b8kcf")
blackboard_plan = SubResource("BlackboardPlan_tnf02")
