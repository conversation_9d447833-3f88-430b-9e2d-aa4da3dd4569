[gd_resource type="BehaviorTree" load_steps=9 format=3 uid="uid://b1i0xo0o676va"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_ewfwq"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"
var/flank_speed/name = &"flank_speed"
var/flank_speed/type = 3
var/flank_speed/value = 600.0
var/flank_speed/hint = 1
var/flank_speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_wu06u"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_dubaq"]
animation_player = SubResource("BBNode_wu06u")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_meli1"]
duration = 5.0

[sub_resource type="BBNode" id="BBNode_mgehh"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_0fqno"]
animation_player = SubResource("BBNode_mgehh")
animation_name = &"dance"

[sub_resource type="BTWait" id="BTWait_m3h4o"]
duration = 5.0

[sub_resource type="BTSequence" id="BTSequence_rbfdh"]
children = [SubResource("BTPlayAnimation_dubaq"), SubResource("BTWait_meli1"), SubResource("BTPlayAnimation_0fqno"), SubResource("BTWait_m3h4o")]

[resource]
description = "Behavior trees are designed to simplify the creation of responsive and modular behaviors for games. They consist of tasks that represent specific actions or decision-making rules. Tasks can be broadly categorized into two groups: control tasks and leaf tasks. [b]Control tasks[/b] dictate the execution flow within the tree, while [b]leaf tasks[/b] represent specific actions to perform (such as moving or attacking) or conditions that need to be checked. Typically, it's the leaf tasks that perform the actual work in behavior trees.

A behavior tree is usually processed each frame (physics frame by default). It is traversed from top to bottom, with the control tasks determining the control flow. Each task has a [color=cyan]_tick[/color] method, which performs the task's work and returns a status indicating its progress: [SUCCESS], [FAILURE], or [RUNNING]. [SUCCESS] and [FAILURE] indicate the outcome of finished work, while [RUNNING] status is returned when a task requires more than one tick to complete its job. These statuses determine how the tree progresses, with the [RUNNING] status usually meaning that the tree will continue execution during the next frame."
blackboard_plan = SubResource("BlackboardPlan_ewfwq")
root_task = SubResource("BTSequence_rbfdh")
