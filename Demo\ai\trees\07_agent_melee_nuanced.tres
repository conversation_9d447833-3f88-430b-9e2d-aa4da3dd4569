[gd_resource type="BehaviorTree" load_steps=49 format=3 uid="uid://c2u6sljqkim0n"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_08fik"]
[ext_resource type="Script" uid="uid://ct71h72pech3b" path="res://demo/ai/tasks/select_flanking_pos.gd" id="2_te3yo"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="3_svwk8"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="4_mvsyw"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="5_r1ou0"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_46tbn"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"
var/flank_speed/name = &"flank_speed"
var/flank_speed/type = 3
var/flank_speed/value = 600.0
var/flank_speed/hint = 1
var/flank_speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_nrd4b"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qiw21"]
animation_player = SubResource("BBNode_nrd4b")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_xlud8"]
min_duration = 0.7
max_duration = 1.5

[sub_resource type="BTAction" id="BTAction_c4cxo"]
script = ExtResource("1_08fik")
group = &"player"
output_var = &"target"

[sub_resource type="BTSequence" id="BTSequence_yhjh1"]
custom_name = "Pause before action"
children = [SubResource("BTPlayAnimation_qiw21"), SubResource("BTRandomWait_xlud8"), SubResource("BTAction_c4cxo")]

[sub_resource type="BBNode" id="BBNode_kc64r"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_panch"]
animation_player = SubResource("BBNode_kc64r")
animation_name = &"walk"
blend = 0.1
speed = 1.5

[sub_resource type="BTAction" id="BTAction_6e48s"]
script = ExtResource("2_te3yo")
target_var = &"target"
flank_side = 1
range_min = 400
range_max = 600
position_var = &"flank_pos"

[sub_resource type="BTAction" id="BTAction_66hsk"]
script = ExtResource("3_svwk8")
target_position_var = &"flank_pos"
speed_var = &"flank_speed"
tolerance = 50.0
avoid_var = &"target"

[sub_resource type="BTTimeLimit" id="BTTimeLimit_24ath"]
children = [SubResource("BTAction_66hsk")]

[sub_resource type="BTAction" id="BTAction_enw2m"]
script = ExtResource("4_mvsyw")
target_var = &"target"

[sub_resource type="BTSequence" id="BTSequence_lhg7f"]
custom_name = "Flank player"
children = [SubResource("BTPlayAnimation_panch"), SubResource("BTAction_6e48s"), SubResource("BTTimeLimit_24ath"), SubResource("BTAction_enw2m")]
metadata/_weight_ = 1.0

[sub_resource type="BTCooldown" id="BTCooldown_skw41"]
children = [SubResource("BTSequence_lhg7f")]
duration = 6.0
metadata/_weight_ = 2.0

[sub_resource type="BBNode" id="BBNode_wpj6d"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_olf37"]
animation_player = SubResource("BBNode_wpj6d")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_a4jqi"]
script = ExtResource("5_r1ou0")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BTTimeLimit" id="BTTimeLimit_xek5v"]
children = [SubResource("BTAction_a4jqi")]
time_limit = 2.0

[sub_resource type="BTAction" id="BTAction_kidxn"]
script = ExtResource("4_mvsyw")
target_var = &"target"

[sub_resource type="BTWait" id="BTWait_tadkc"]
duration = 0.1

[sub_resource type="BBNode" id="BBNode_g8qww"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_a8yqu"]
await_completion = 2.0
animation_player = SubResource("BBNode_g8qww")
animation_name = &"attack_2"

[sub_resource type="BTWait" id="BTWait_vjstl"]
duration = 0.1

[sub_resource type="BBNode" id="BBNode_rfop0"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_8oyw6"]
await_completion = 2.0
animation_player = SubResource("BBNode_rfop0")
animation_name = &"attack_3"

[sub_resource type="BTSequence" id="BTSequence_1xfnq"]
custom_name = "Approach and melee attack"
children = [SubResource("BTPlayAnimation_olf37"), SubResource("BTTimeLimit_xek5v"), SubResource("BTAction_kidxn"), SubResource("BTWait_tadkc"), SubResource("BTPlayAnimation_a8yqu"), SubResource("BTWait_vjstl"), SubResource("BTPlayAnimation_8oyw6")]
metadata/_weight_ = 4.0

[sub_resource type="BTAction" id="BTAction_mf87t"]
script = ExtResource("2_te3yo")
target_var = &"target"
flank_side = 0
range_min = 350
range_max = 600
position_var = &"pos"

[sub_resource type="BBNode" id="BBNode_cx111"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_mfbeu"]
animation_player = SubResource("BBNode_cx111")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_6nx58"]
script = ExtResource("3_svwk8")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTTimeLimit" id="BTTimeLimit_tidwl"]
children = [SubResource("BTAction_6nx58")]
time_limit = 3.0

[sub_resource type="BTAction" id="BTAction_8q20y"]
script = ExtResource("4_mvsyw")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_s6vt4"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qa8jy"]
animation_player = SubResource("BBNode_s6vt4")
animation_name = &"throw_prepare"
blend = 0.1
speed = 0.7

[sub_resource type="BTWait" id="BTWait_gbcyb"]

[sub_resource type="BBNode" id="BBNode_qkfqt"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_0ktds"]
await_completion = 1.0
animation_player = SubResource("BBNode_qkfqt")
animation_name = &"throw"
blend = 0.05

[sub_resource type="BBNode" id="BBNode_1yxc5"]
resource_name = "."
saved_value = NodePath(".")

[sub_resource type="BTCallMethod" id="BTCallMethod_yx4fk"]
node = SubResource("BBNode_1yxc5")
method = &"throw_ninja_star"

[sub_resource type="BTRandomWait" id="BTRandomWait_2pmoe"]
max_duration = 1.5

[sub_resource type="BTSequence" id="BTSequence_rgbq3"]
custom_name = "Throw ninja star"
children = [SubResource("BTAction_mf87t"), SubResource("BTPlayAnimation_mfbeu"), SubResource("BTTimeLimit_tidwl"), SubResource("BTAction_8q20y"), SubResource("BTPlayAnimation_qa8jy"), SubResource("BTWait_gbcyb"), SubResource("BTPlayAnimation_0ktds"), SubResource("BTCallMethod_yx4fk"), SubResource("BTRandomWait_2pmoe")]
metadata/_weight_ = 2.0

[sub_resource type="BTProbabilitySelector" id="BTProbabilitySelector_rjsiq"]
children = [SubResource("BTCooldown_skw41"), SubResource("BTSequence_1xfnq"), SubResource("BTSequence_rgbq3")]
abort_on_failure = true

[sub_resource type="BTSequence" id="BTSequence_pxl2k"]
custom_name = "Main"
children = [SubResource("BTSequence_yhjh1"), SubResource("BTProbabilitySelector_rjsiq")]

[resource]
description = "[comp]ProbabilitySelector[/comp] chooses a child task to execute based on attached probabilities. Probability distribution is calculated based on weights assigned to each child task.
"
blackboard_plan = SubResource("BlackboardPlan_46tbn")
root_task = SubResource("BTSequence_pxl2k")
