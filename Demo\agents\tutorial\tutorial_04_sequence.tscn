[gd_scene load_steps=5 format=3 uid="uid://bhtm8gnk5hsus"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_oibr1"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_j52yc"]
[ext_resource type="BehaviorTree" uid="uid://dln8ywvtqedt7" path="res://demo/ai/trees/tutorial/tutorial_04_sequence.tres" id="3_feewj"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialSequence" instance=ExtResource("1_oibr1")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_j52yc")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_j52yc")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_j52yc")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_j52yc")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_j52yc")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_j52yc")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_feewj")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
