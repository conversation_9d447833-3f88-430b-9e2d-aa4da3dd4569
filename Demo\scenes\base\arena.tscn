[gd_scene load_steps=8 format=3 uid="uid://bsig1usigbbuy"]

[ext_resource type="Texture2D" uid="uid://65b6yuobhxf8" path="res://demo/assets/env_clouds.png" id="1_1blat"]
[ext_resource type="Texture2D" uid="uid://b3g14elmg0m36" path="res://demo/assets/env_rocks.png" id="1_145kx"]
[ext_resource type="Texture2D" uid="uid://cc7b22sy46gwn" path="res://demo/assets/env_ground.png" id="1_vifjc"]
[ext_resource type="Texture2D" uid="uid://4kw2ks8doc0w" path="res://demo/assets/env_plants.png" id="2_kesm7"]

[sub_resource type="Animation" id="Animation_gwtgs"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Environment/Clouds/Cloud11:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3784, 345)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Environment/Clouds/Cloud1:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(764, 358)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Environment/Clouds/Cloud13:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(4005, 2983)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Environment/Clouds/Cloud5:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-152, 2723)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Environment/Clouds/Cloud6:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3293, 936)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Environment/Clouds/Cloud2:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(85, 1505)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Environment/Clouds/Cloud16:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3434, 3542)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Environment/Clouds/Cloud7:position")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1380, 3802)]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Environment/Clouds/Cloud3:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(503, 1222)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Environment/Clouds/Cloud14:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(4174, 3309)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Environment/Clouds/Cloud15:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(4695, 2800)]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Environment/Clouds/Cloud8:position")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(152, 3383)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Environment/Clouds/Cloud4:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(164, 1669)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Environment/Clouds/Cloud9:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(328, 3474)]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Environment/Clouds/Cloud10:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(933, 515)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Environment/Clouds/Cloud12:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(4570, 1222)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Environment/Clouds/Cloud11:scale")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Environment/Clouds/Cloud1:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Environment/Clouds/Cloud13:scale")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Environment/Clouds/Cloud5:scale")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Environment/Clouds/Cloud6:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Environment/Clouds/Cloud2:scale")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Environment/Clouds/Cloud16:scale")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/23/type = "value"
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/path = NodePath("Environment/Clouds/Cloud7:scale")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/24/type = "value"
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/path = NodePath("Environment/Clouds/Cloud3:scale")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/25/type = "value"
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/path = NodePath("Environment/Clouds/Cloud14:scale")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/26/type = "value"
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/path = NodePath("Environment/Clouds/Cloud15:scale")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/27/type = "value"
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/path = NodePath("Environment/Clouds/Cloud8:scale")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/28/type = "value"
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/path = NodePath("Environment/Clouds/Cloud4:scale")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/29/type = "value"
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/path = NodePath("Environment/Clouds/Cloud9:scale")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/30/type = "value"
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/path = NodePath("Environment/Clouds/Cloud10:scale")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/31/type = "value"
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/path = NodePath("Environment/Clouds/Cloud12:scale")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/32/type = "value"
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/path = NodePath("Environment/Clouds/Cloud1:visible")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/33/type = "value"
tracks/33/imported = false
tracks/33/enabled = true
tracks/33/path = NodePath("Environment/Clouds/Cloud13:visible")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/34/type = "value"
tracks/34/imported = false
tracks/34/enabled = true
tracks/34/path = NodePath("Environment/Clouds/Cloud5:visible")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/35/type = "value"
tracks/35/imported = false
tracks/35/enabled = true
tracks/35/path = NodePath("Environment/Clouds/Cloud6:visible")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/36/type = "value"
tracks/36/imported = false
tracks/36/enabled = true
tracks/36/path = NodePath("Environment/Clouds/Cloud2:visible")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/37/type = "value"
tracks/37/imported = false
tracks/37/enabled = true
tracks/37/path = NodePath("Environment/Clouds/Cloud16:visible")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/38/type = "value"
tracks/38/imported = false
tracks/38/enabled = true
tracks/38/path = NodePath("Environment/Clouds/Cloud7:visible")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/39/type = "value"
tracks/39/imported = false
tracks/39/enabled = true
tracks/39/path = NodePath("Environment/Clouds/Cloud3:visible")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/40/type = "value"
tracks/40/imported = false
tracks/40/enabled = true
tracks/40/path = NodePath("Environment/Clouds/Cloud14:visible")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/41/type = "value"
tracks/41/imported = false
tracks/41/enabled = true
tracks/41/path = NodePath("Environment/Clouds/Cloud15:visible")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/42/type = "value"
tracks/42/imported = false
tracks/42/enabled = true
tracks/42/path = NodePath("Environment/Clouds/Cloud8:visible")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/43/type = "value"
tracks/43/imported = false
tracks/43/enabled = true
tracks/43/path = NodePath("Environment/Clouds/Cloud4:visible")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/44/type = "value"
tracks/44/imported = false
tracks/44/enabled = true
tracks/44/path = NodePath("Environment/Clouds/Cloud9:visible")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/45/type = "value"
tracks/45/imported = false
tracks/45/enabled = true
tracks/45/path = NodePath("Environment/Clouds/Cloud10:visible")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/46/type = "value"
tracks/46/imported = false
tracks/46/enabled = true
tracks/46/path = NodePath("Environment/Clouds/Cloud12:visible")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/47/type = "value"
tracks/47/imported = false
tracks/47/enabled = true
tracks/47/path = NodePath("Environment/Clouds/Cloud11:visible")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/48/type = "value"
tracks/48/imported = false
tracks/48/enabled = true
tracks/48/path = NodePath("Environment/Clouds/Cloud17:position")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-2737.13, 367.814)]
}
tracks/49/type = "value"
tracks/49/imported = false
tracks/49/enabled = true
tracks/49/path = NodePath("Environment/Clouds/Cloud17:scale")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1.35085, 1.15254)]
}
tracks/50/type = "value"
tracks/50/imported = false
tracks/50/enabled = true
tracks/50/path = NodePath("Environment/Clouds/Cloud18:position")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-1254.92, 3539.61)]
}
tracks/51/type = "value"
tracks/51/imported = false
tracks/51/enabled = true
tracks/51/path = NodePath("Environment/Clouds/Cloud18:scale")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.898305, 0.898305)]
}
tracks/52/type = "value"
tracks/52/imported = false
tracks/52/enabled = true
tracks/52/path = NodePath("Environment/Clouds/Cloud19:position")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-2912.7, 1260.29)]
}
tracks/53/type = "value"
tracks/53/imported = false
tracks/53/enabled = true
tracks/53/path = NodePath("Environment/Clouds/Cloud19:scale")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1.10169, 1.10169)]
}
tracks/54/type = "value"
tracks/54/imported = false
tracks/54/enabled = true
tracks/54/path = NodePath("Environment/Clouds/Cloud20:position")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-3251.5, 3437.56)]
}
tracks/55/type = "value"
tracks/55/imported = false
tracks/55/enabled = true
tracks/55/path = NodePath("Environment/Clouds/Cloud20:scale")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/56/type = "value"
tracks/56/imported = false
tracks/56/enabled = true
tracks/56/path = NodePath("Environment/Clouds/Cloud21:position")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-3181.26, 3581.44)]
}
tracks/57/type = "value"
tracks/57/imported = false
tracks/57/enabled = true
tracks/57/path = NodePath("Environment/Clouds/Cloud21:scale")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.949153, 0.949153)]
}
tracks/58/type = "value"
tracks/58/imported = false
tracks/58/enabled = true
tracks/58/path = NodePath("Environment/Clouds/Cloud22:position")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-420.856, 367.017)]
}
tracks/59/type = "value"
tracks/59/imported = false
tracks/59/enabled = true
tracks/59/path = NodePath("Environment/Clouds/Cloud22:scale")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/60/type = "value"
tracks/60/imported = false
tracks/60/enabled = true
tracks/60/path = NodePath("Environment/Clouds/Cloud17:visible")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/61/type = "value"
tracks/61/imported = false
tracks/61/enabled = true
tracks/61/path = NodePath("Environment/Clouds/Cloud18:visible")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/62/type = "value"
tracks/62/imported = false
tracks/62/enabled = true
tracks/62/path = NodePath("Environment/Clouds/Cloud19:visible")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/63/type = "value"
tracks/63/imported = false
tracks/63/enabled = true
tracks/63/path = NodePath("Environment/Clouds/Cloud20:visible")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/64/type = "value"
tracks/64/imported = false
tracks/64/enabled = true
tracks/64/path = NodePath("Environment/Clouds/Cloud21:visible")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/65/type = "value"
tracks/65/imported = false
tracks/65/enabled = true
tracks/65/path = NodePath("Environment/Clouds/Cloud22:visible")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/66/type = "value"
tracks/66/imported = false
tracks/66/enabled = true
tracks/66/path = NodePath("Environment/Clouds/Cloud28:position")
tracks/66/interp = 1
tracks/66/loop_wrap = true
tracks/66/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-461, 1787)]
}
tracks/67/type = "value"
tracks/67/imported = false
tracks/67/enabled = true
tracks/67/path = NodePath("Environment/Clouds/Cloud28:scale")
tracks/67/interp = 1
tracks/67/loop_wrap = true
tracks/67/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1.10169, 1.10169)]
}
tracks/68/type = "value"
tracks/68/imported = false
tracks/68/enabled = true
tracks/68/path = NodePath("Environment/Clouds/Cloud23:position")
tracks/68/interp = 1
tracks/68/loop_wrap = true
tracks/68/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-1493, 3040)]
}
tracks/69/type = "value"
tracks/69/imported = false
tracks/69/enabled = true
tracks/69/path = NodePath("Environment/Clouds/Cloud23:scale")
tracks/69/interp = 1
tracks/69/loop_wrap = true
tracks/69/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.898305, 0.898305)]
}
tracks/70/type = "value"
tracks/70/imported = false
tracks/70/enabled = true
tracks/70/path = NodePath("Environment/Clouds/Cloud24:position")
tracks/70/interp = 1
tracks/70/loop_wrap = true
tracks/70/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-510, 2429)]
}
tracks/71/type = "value"
tracks/71/imported = false
tracks/71/enabled = true
tracks/71/path = NodePath("Environment/Clouds/Cloud24:scale")
tracks/71/interp = 1
tracks/71/loop_wrap = true
tracks/71/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1.15254, 1.15254)]
}
tracks/72/type = "value"
tracks/72/imported = false
tracks/72/enabled = true
tracks/72/path = NodePath("Environment/Clouds/Cloud25:position")
tracks/72/interp = 1
tracks/72/loop_wrap = true
tracks/72/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-712, 1373)]
}
tracks/73/type = "value"
tracks/73/imported = false
tracks/73/enabled = true
tracks/73/path = NodePath("Environment/Clouds/Cloud25:scale")
tracks/73/interp = 1
tracks/73/loop_wrap = true
tracks/73/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1.10169, 1.10169)]
}
tracks/74/type = "value"
tracks/74/imported = false
tracks/74/enabled = true
tracks/74/path = NodePath("Environment/Clouds/Cloud26:position")
tracks/74/interp = 1
tracks/74/loop_wrap = true
tracks/74/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-269, 2004)]
}
tracks/75/type = "value"
tracks/75/imported = false
tracks/75/enabled = true
tracks/75/path = NodePath("Environment/Clouds/Cloud26:scale")
tracks/75/interp = 1
tracks/75/loop_wrap = true
tracks/75/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.79661, 0.79661)]
}
tracks/76/type = "value"
tracks/76/imported = false
tracks/76/enabled = true
tracks/76/path = NodePath("Environment/Clouds/Cloud27:position")
tracks/76/interp = 1
tracks/76/loop_wrap = true
tracks/76/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-1656, 2353)]
}
tracks/77/type = "value"
tracks/77/imported = false
tracks/77/enabled = true
tracks/77/path = NodePath("Environment/Clouds/Cloud27:scale")
tracks/77/interp = 1
tracks/77/loop_wrap = true
tracks/77/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.949153, 0.949153)]
}
tracks/78/type = "value"
tracks/78/imported = false
tracks/78/enabled = true
tracks/78/path = NodePath("Environment/Clouds/Cloud23:visible")
tracks/78/interp = 1
tracks/78/loop_wrap = true
tracks/78/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/79/type = "value"
tracks/79/imported = false
tracks/79/enabled = true
tracks/79/path = NodePath("Environment/Clouds/Cloud24:visible")
tracks/79/interp = 1
tracks/79/loop_wrap = true
tracks/79/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/80/type = "value"
tracks/80/imported = false
tracks/80/enabled = true
tracks/80/path = NodePath("Environment/Clouds/Cloud25:visible")
tracks/80/interp = 1
tracks/80/loop_wrap = true
tracks/80/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/81/type = "value"
tracks/81/imported = false
tracks/81/enabled = true
tracks/81/path = NodePath("Environment/Clouds/Cloud26:visible")
tracks/81/interp = 1
tracks/81/loop_wrap = true
tracks/81/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/82/type = "value"
tracks/82/imported = false
tracks/82/enabled = true
tracks/82/path = NodePath("Environment/Clouds/Cloud27:visible")
tracks/82/interp = 1
tracks/82/loop_wrap = true
tracks/82/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/83/type = "value"
tracks/83/imported = false
tracks/83/enabled = true
tracks/83/path = NodePath("Environment/Clouds/Cloud28:visible")
tracks/83/interp = 1
tracks/83/loop_wrap = true
tracks/83/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/84/type = "value"
tracks/84/imported = false
tracks/84/enabled = true
tracks/84/path = NodePath("Environment/Clouds/Cloud29:position")
tracks/84/interp = 1
tracks/84/loop_wrap = true
tracks/84/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3890, 2612.5)]
}
tracks/85/type = "value"
tracks/85/imported = false
tracks/85/enabled = true
tracks/85/path = NodePath("Environment/Clouds/Cloud29:scale")
tracks/85/interp = 1
tracks/85/loop_wrap = true
tracks/85/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/86/type = "value"
tracks/86/imported = false
tracks/86/enabled = true
tracks/86/path = NodePath("Environment/Clouds/Cloud30:position")
tracks/86/interp = 1
tracks/86/loop_wrap = true
tracks/86/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3832, 1394.5)]
}
tracks/87/type = "value"
tracks/87/imported = false
tracks/87/enabled = true
tracks/87/path = NodePath("Environment/Clouds/Cloud30:scale")
tracks/87/interp = 1
tracks/87/loop_wrap = true
tracks/87/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/88/type = "value"
tracks/88/imported = false
tracks/88/enabled = true
tracks/88/path = NodePath("Environment/Clouds/Cloud31:position")
tracks/88/interp = 1
tracks/88/loop_wrap = true
tracks/88/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3472, 3106)]
}
tracks/89/type = "value"
tracks/89/imported = false
tracks/89/enabled = true
tracks/89/path = NodePath("Environment/Clouds/Cloud31:scale")
tracks/89/interp = 1
tracks/89/loop_wrap = true
tracks/89/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/90/type = "value"
tracks/90/imported = false
tracks/90/enabled = true
tracks/90/path = NodePath("Environment/Clouds/Cloud32:position")
tracks/90/interp = 1
tracks/90/loop_wrap = true
tracks/90/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3095, 2689.5)]
}
tracks/91/type = "value"
tracks/91/imported = false
tracks/91/enabled = true
tracks/91/path = NodePath("Environment/Clouds/Cloud32:scale")
tracks/91/interp = 1
tracks/91/loop_wrap = true
tracks/91/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/92/type = "value"
tracks/92/imported = false
tracks/92/enabled = true
tracks/92/path = NodePath("Environment/Clouds/Cloud33:position")
tracks/92/interp = 1
tracks/92/loop_wrap = true
tracks/92/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(4016, 1558.5)]
}
tracks/93/type = "value"
tracks/93/imported = false
tracks/93/enabled = true
tracks/93/path = NodePath("Environment/Clouds/Cloud33:scale")
tracks/93/interp = 1
tracks/93/loop_wrap = true
tracks/93/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/94/type = "value"
tracks/94/imported = false
tracks/94/enabled = true
tracks/94/path = NodePath("Environment/Clouds/Cloud29:visible")
tracks/94/interp = 1
tracks/94/loop_wrap = true
tracks/94/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/95/type = "value"
tracks/95/imported = false
tracks/95/enabled = true
tracks/95/path = NodePath("Environment/Clouds/Cloud30:visible")
tracks/95/interp = 1
tracks/95/loop_wrap = true
tracks/95/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/96/type = "value"
tracks/96/imported = false
tracks/96/enabled = true
tracks/96/path = NodePath("Environment/Clouds/Cloud31:visible")
tracks/96/interp = 1
tracks/96/loop_wrap = true
tracks/96/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/97/type = "value"
tracks/97/imported = false
tracks/97/enabled = true
tracks/97/path = NodePath("Environment/Clouds/Cloud32:visible")
tracks/97/interp = 1
tracks/97/loop_wrap = true
tracks/97/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/98/type = "value"
tracks/98/imported = false
tracks/98/enabled = true
tracks/98/path = NodePath("Environment/Clouds/Cloud33:visible")
tracks/98/interp = 1
tracks/98/loop_wrap = true
tracks/98/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/99/type = "value"
tracks/99/imported = false
tracks/99/enabled = true
tracks/99/path = NodePath("Environment/Clouds/Cloud34:position")
tracks/99/interp = 1
tracks/99/loop_wrap = true
tracks/99/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3863, 1375)]
}
tracks/100/type = "value"
tracks/100/imported = false
tracks/100/enabled = true
tracks/100/path = NodePath("Environment/Clouds/Cloud34:scale")
tracks/100/interp = 1
tracks/100/loop_wrap = true
tracks/100/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/101/type = "value"
tracks/101/imported = false
tracks/101/enabled = true
tracks/101/path = NodePath("Environment/Clouds/Cloud35:position")
tracks/101/interp = 1
tracks/101/loop_wrap = true
tracks/101/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3521.25, 3087)]
}
tracks/102/type = "value"
tracks/102/imported = false
tracks/102/enabled = true
tracks/102/path = NodePath("Environment/Clouds/Cloud35:scale")
tracks/102/interp = 1
tracks/102/loop_wrap = true
tracks/102/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/103/type = "value"
tracks/103/imported = false
tracks/103/enabled = true
tracks/103/path = NodePath("Environment/Clouds/Cloud36:position")
tracks/103/interp = 1
tracks/103/loop_wrap = true
tracks/103/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3609.25, 3161)]
}
tracks/104/type = "value"
tracks/104/imported = false
tracks/104/enabled = true
tracks/104/path = NodePath("Environment/Clouds/Cloud36:scale")
tracks/104/interp = 1
tracks/104/loop_wrap = true
tracks/104/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/105/type = "value"
tracks/105/imported = false
tracks/105/enabled = true
tracks/105/path = NodePath("Environment/Clouds/Cloud37:position")
tracks/105/interp = 1
tracks/105/loop_wrap = true
tracks/105/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3754.25, 1447)]
}
tracks/106/type = "value"
tracks/106/imported = false
tracks/106/enabled = true
tracks/106/path = NodePath("Environment/Clouds/Cloud37:scale")
tracks/106/interp = 1
tracks/106/loop_wrap = true
tracks/106/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/107/type = "value"
tracks/107/imported = false
tracks/107/enabled = true
tracks/107/path = NodePath("Environment/Clouds/Cloud38:position")
tracks/107/interp = 1
tracks/107/loop_wrap = true
tracks/107/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2915.25, 2833)]
}
tracks/108/type = "value"
tracks/108/imported = false
tracks/108/enabled = true
tracks/108/path = NodePath("Environment/Clouds/Cloud38:scale")
tracks/108/interp = 1
tracks/108/loop_wrap = true
tracks/108/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.898305, 0.898305)]
}
tracks/109/type = "value"
tracks/109/imported = false
tracks/109/enabled = true
tracks/109/path = NodePath("Environment/Clouds/Cloud39:position")
tracks/109/interp = 1
tracks/109/loop_wrap = true
tracks/109/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(3270.25, 2207)]
}
tracks/110/type = "value"
tracks/110/imported = false
tracks/110/enabled = true
tracks/110/path = NodePath("Environment/Clouds/Cloud39:scale")
tracks/110/interp = 1
tracks/110/loop_wrap = true
tracks/110/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/111/type = "value"
tracks/111/imported = false
tracks/111/enabled = true
tracks/111/path = NodePath("Environment/Clouds/Cloud34:visible")
tracks/111/interp = 1
tracks/111/loop_wrap = true
tracks/111/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/112/type = "value"
tracks/112/imported = false
tracks/112/enabled = true
tracks/112/path = NodePath("Environment/Clouds/Cloud35:visible")
tracks/112/interp = 1
tracks/112/loop_wrap = true
tracks/112/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/113/type = "value"
tracks/113/imported = false
tracks/113/enabled = true
tracks/113/path = NodePath("Environment/Clouds/Cloud36:visible")
tracks/113/interp = 1
tracks/113/loop_wrap = true
tracks/113/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/114/type = "value"
tracks/114/imported = false
tracks/114/enabled = true
tracks/114/path = NodePath("Environment/Clouds/Cloud37:visible")
tracks/114/interp = 1
tracks/114/loop_wrap = true
tracks/114/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/115/type = "value"
tracks/115/imported = false
tracks/115/enabled = true
tracks/115/path = NodePath("Environment/Clouds/Cloud38:visible")
tracks/115/interp = 1
tracks/115/loop_wrap = true
tracks/115/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/116/type = "value"
tracks/116/imported = false
tracks/116/enabled = true
tracks/116/path = NodePath("Environment/Clouds/Cloud39:visible")
tracks/116/interp = 1
tracks/116/loop_wrap = true
tracks/116/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_75lmk"]
resource_name = "float"
length = 60.2
loop_mode = 1
step = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Environment/Clouds/Cloud11:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-878, 345), Vector2(8280, 329), Vector2(-878, 345)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Environment/Clouds/Cloud1:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-149, 377), Vector2(6644, 318), Vector2(-149, 377)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Environment/Clouds/Cloud13:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-856, 3012), Vector2(6701, 2975), Vector2(-856, 3012)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Environment/Clouds/Cloud5:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-152, 2723), Vector2(6304, 2691), Vector2(-152, 2723)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Environment/Clouds/Cloud6:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-1169, 936), Vector2(6941, 936), Vector2(-1169, 936)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Environment/Clouds/Cloud2:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-210, 1505), Vector2(7109, 1489), Vector2(-210, 1505)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Environment/Clouds/Cloud16:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-733, 3542), Vector2(6978, 3478), Vector2(-733, 3542)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Environment/Clouds/Cloud7:position")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-1427, 3831), Vector2(6420, 3842), Vector2(-1427, 3831)]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Environment/Clouds/Cloud3:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-258, 1222), Vector2(6551, 1238), Vector2(-258, 1222)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Environment/Clouds/Cloud14:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-259, 3309), Vector2(7190, 3301), Vector2(-259, 3309)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Environment/Clouds/Cloud15:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-947, 2800), Vector2(7711, 2824), Vector2(-947, 2800)]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Environment/Clouds/Cloud8:position")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-171, 3383), Vector2(6224, 3431), Vector2(-171, 3383)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Environment/Clouds/Cloud4:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-26, 1669), Vector2(6716, 1797), Vector2(-26, 1669)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Environment/Clouds/Cloud9:position")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-500, 3474), Vector2(6192, 3626), Vector2(-500, 3474)]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Environment/Clouds/Cloud10:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-285, 515), Vector2(6173, 595), Vector2(-285, 515)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Environment/Clouds/Cloud12:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-444, 1222), Vector2(8090, 1286), Vector2(-444, 1222)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Environment/Clouds/Cloud11:scale")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Environment/Clouds/Cloud1:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.69, 1.3), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Environment/Clouds/Cloud13:scale")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Environment/Clouds/Cloud5:scale")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.8, 0.8), Vector2(1, 1)]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Environment/Clouds/Cloud6:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Environment/Clouds/Cloud2:scale")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Environment/Clouds/Cloud16:scale")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.8, 0.8), Vector2(1, 1)]
}
tracks/23/type = "value"
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/path = NodePath("Environment/Clouds/Cloud7:scale")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/24/type = "value"
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/path = NodePath("Environment/Clouds/Cloud3:scale")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/25/type = "value"
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/path = NodePath("Environment/Clouds/Cloud14:scale")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/26/type = "value"
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/path = NodePath("Environment/Clouds/Cloud15:scale")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.6, 0.6), Vector2(1, 1)]
}
tracks/27/type = "value"
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/path = NodePath("Environment/Clouds/Cloud8:scale")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/28/type = "value"
tracks/28/imported = false
tracks/28/enabled = true
tracks/28/path = NodePath("Environment/Clouds/Cloud4:scale")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/29/type = "value"
tracks/29/imported = false
tracks/29/enabled = true
tracks/29/path = NodePath("Environment/Clouds/Cloud9:scale")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.9, 0.9), Vector2(1, 1)]
}
tracks/30/type = "value"
tracks/30/imported = false
tracks/30/enabled = true
tracks/30/path = NodePath("Environment/Clouds/Cloud10:scale")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/31/type = "value"
tracks/31/imported = false
tracks/31/enabled = true
tracks/31/path = NodePath("Environment/Clouds/Cloud12:scale")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = {
"times": PackedFloat32Array(0, 59, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/32/type = "value"
tracks/32/imported = false
tracks/32/enabled = true
tracks/32/path = NodePath("Environment/Clouds/Cloud1:visible")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/33/type = "value"
tracks/33/imported = false
tracks/33/enabled = true
tracks/33/path = NodePath("Environment/Clouds/Cloud13:visible")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/34/type = "value"
tracks/34/imported = false
tracks/34/enabled = true
tracks/34/path = NodePath("Environment/Clouds/Cloud5:visible")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/35/type = "value"
tracks/35/imported = false
tracks/35/enabled = true
tracks/35/path = NodePath("Environment/Clouds/Cloud6:visible")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/36/type = "value"
tracks/36/imported = false
tracks/36/enabled = true
tracks/36/path = NodePath("Environment/Clouds/Cloud2:visible")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/37/type = "value"
tracks/37/imported = false
tracks/37/enabled = true
tracks/37/path = NodePath("Environment/Clouds/Cloud16:visible")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/38/type = "value"
tracks/38/imported = false
tracks/38/enabled = true
tracks/38/path = NodePath("Environment/Clouds/Cloud7:visible")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/39/type = "value"
tracks/39/imported = false
tracks/39/enabled = true
tracks/39/path = NodePath("Environment/Clouds/Cloud3:visible")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/40/type = "value"
tracks/40/imported = false
tracks/40/enabled = true
tracks/40/path = NodePath("Environment/Clouds/Cloud14:visible")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/41/type = "value"
tracks/41/imported = false
tracks/41/enabled = true
tracks/41/path = NodePath("Environment/Clouds/Cloud15:visible")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/42/type = "value"
tracks/42/imported = false
tracks/42/enabled = true
tracks/42/path = NodePath("Environment/Clouds/Cloud8:visible")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/43/type = "value"
tracks/43/imported = false
tracks/43/enabled = true
tracks/43/path = NodePath("Environment/Clouds/Cloud4:visible")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/44/type = "value"
tracks/44/imported = false
tracks/44/enabled = true
tracks/44/path = NodePath("Environment/Clouds/Cloud9:visible")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/45/type = "value"
tracks/45/imported = false
tracks/45/enabled = true
tracks/45/path = NodePath("Environment/Clouds/Cloud10:visible")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/46/type = "value"
tracks/46/imported = false
tracks/46/enabled = true
tracks/46/path = NodePath("Environment/Clouds/Cloud12:visible")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/47/type = "value"
tracks/47/imported = false
tracks/47/enabled = true
tracks/47/path = NodePath("Environment/Clouds/Cloud11:visible")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = {
"times": PackedFloat32Array(0, 59, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/48/type = "value"
tracks/48/imported = false
tracks/48/enabled = true
tracks/48/path = NodePath("Environment/Clouds/Cloud17:position")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-1928, 1432), Vector2(-1928, 1432), Vector2(4049, 1536), Vector2(-1928, 1432)]
}
tracks/49/type = "value"
tracks/49/imported = false
tracks/49/enabled = true
tracks/49/path = NodePath("Environment/Clouds/Cloud17:scale")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1.35085, 1.15254), Vector2(1.35085, 1.15254), Vector2(1.35085, 1.15254), Vector2(1.35085, 1.15254)]
}
tracks/50/type = "value"
tracks/50/imported = false
tracks/50/enabled = true
tracks/50/path = NodePath("Environment/Clouds/Cloud18:position")
tracks/50/interp = 1
tracks/50/loop_wrap = true
tracks/50/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-865, 3055), Vector2(-865, 3055), Vector2(3895.5, 3103.47), Vector2(-865, 3055)]
}
tracks/51/type = "value"
tracks/51/imported = false
tracks/51/enabled = true
tracks/51/path = NodePath("Environment/Clouds/Cloud18:scale")
tracks/51/interp = 1
tracks/51/loop_wrap = true
tracks/51/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(0.898305, 0.898305), Vector2(0.898305, 0.898305), Vector2(0.898305, 0.898305), Vector2(0.898305, 0.898305)]
}
tracks/52/type = "value"
tracks/52/imported = false
tracks/52/enabled = true
tracks/52/path = NodePath("Environment/Clouds/Cloud19:position")
tracks/52/interp = 1
tracks/52/loop_wrap = true
tracks/52/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-1050, 1157), Vector2(-1050, 1157), Vector2(3710.5, 1205.47), Vector2(-1050, 1157)]
}
tracks/53/type = "value"
tracks/53/imported = false
tracks/53/enabled = true
tracks/53/path = NodePath("Environment/Clouds/Cloud19:scale")
tracks/53/interp = 1
tracks/53/loop_wrap = true
tracks/53/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1.10169, 1.10169), Vector2(1.10169, 1.10169), Vector2(1.10169, 1.10169), Vector2(1.10169, 1.10169)]
}
tracks/54/type = "value"
tracks/54/imported = false
tracks/54/enabled = true
tracks/54/path = NodePath("Environment/Clouds/Cloud20:position")
tracks/54/interp = 1
tracks/54/loop_wrap = true
tracks/54/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-2964, 2611), Vector2(-2964, 2611), Vector2(4117, 2739), Vector2(-2964, 2611)]
}
tracks/55/type = "value"
tracks/55/imported = false
tracks/55/enabled = true
tracks/55/path = NodePath("Environment/Clouds/Cloud20:scale")
tracks/55/interp = 1
tracks/55/loop_wrap = true
tracks/55/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/56/type = "value"
tracks/56/imported = false
tracks/56/enabled = true
tracks/56/path = NodePath("Environment/Clouds/Cloud21:position")
tracks/56/interp = 1
tracks/56/loop_wrap = true
tracks/56/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-493, 2589), Vector2(-493, 2589), Vector2(4267.5, 2637.47), Vector2(-493, 2589)]
}
tracks/57/type = "value"
tracks/57/imported = false
tracks/57/enabled = true
tracks/57/path = NodePath("Environment/Clouds/Cloud21:scale")
tracks/57/interp = 1
tracks/57/loop_wrap = true
tracks/57/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(0.949153, 0.949153), Vector2(0.949153, 0.949153), Vector2(0.949153, 0.949153), Vector2(0.949153, 0.949153)]
}
tracks/58/type = "value"
tracks/58/imported = false
tracks/58/enabled = true
tracks/58/path = NodePath("Environment/Clouds/Cloud22:position")
tracks/58/interp = 1
tracks/58/loop_wrap = true
tracks/58/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-1333, 2015), Vector2(-1333, 2015), Vector2(3427.5, 2063.47), Vector2(-1333, 2015)]
}
tracks/59/type = "value"
tracks/59/imported = false
tracks/59/enabled = true
tracks/59/path = NodePath("Environment/Clouds/Cloud22:scale")
tracks/59/interp = 1
tracks/59/loop_wrap = true
tracks/59/keys = {
"times": PackedFloat32Array(0, 12, 47, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/60/type = "value"
tracks/60/imported = false
tracks/60/enabled = true
tracks/60/path = NodePath("Environment/Clouds/Cloud17:visible")
tracks/60/interp = 1
tracks/60/loop_wrap = true
tracks/60/keys = {
"times": PackedFloat32Array(0, 47, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/61/type = "value"
tracks/61/imported = false
tracks/61/enabled = true
tracks/61/path = NodePath("Environment/Clouds/Cloud18:visible")
tracks/61/interp = 1
tracks/61/loop_wrap = true
tracks/61/keys = {
"times": PackedFloat32Array(0, 47, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/62/type = "value"
tracks/62/imported = false
tracks/62/enabled = true
tracks/62/path = NodePath("Environment/Clouds/Cloud19:visible")
tracks/62/interp = 1
tracks/62/loop_wrap = true
tracks/62/keys = {
"times": PackedFloat32Array(0, 47, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/63/type = "value"
tracks/63/imported = false
tracks/63/enabled = true
tracks/63/path = NodePath("Environment/Clouds/Cloud20:visible")
tracks/63/interp = 1
tracks/63/loop_wrap = true
tracks/63/keys = {
"times": PackedFloat32Array(0, 47, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/64/type = "value"
tracks/64/imported = false
tracks/64/enabled = true
tracks/64/path = NodePath("Environment/Clouds/Cloud21:visible")
tracks/64/interp = 1
tracks/64/loop_wrap = true
tracks/64/keys = {
"times": PackedFloat32Array(0, 47, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/65/type = "value"
tracks/65/imported = false
tracks/65/enabled = true
tracks/65/path = NodePath("Environment/Clouds/Cloud22:visible")
tracks/65/interp = 1
tracks/65/loop_wrap = true
tracks/65/keys = {
"times": PackedFloat32Array(0, 47, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/66/type = "value"
tracks/66/imported = false
tracks/66/enabled = true
tracks/66/path = NodePath("Environment/Clouds/Cloud28:position")
tracks/66/interp = 1
tracks/66/loop_wrap = true
tracks/66/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-461, 1787), Vector2(-461, 1787), Vector2(3829.5, 1787), Vector2(-461, 1787)]
}
tracks/67/type = "value"
tracks/67/imported = false
tracks/67/enabled = true
tracks/67/path = NodePath("Environment/Clouds/Cloud28:scale")
tracks/67/interp = 1
tracks/67/loop_wrap = true
tracks/67/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(0.9, 0.9), Vector2(1, 1)]
}
tracks/68/type = "value"
tracks/68/imported = false
tracks/68/enabled = true
tracks/68/path = NodePath("Environment/Clouds/Cloud23:position")
tracks/68/interp = 1
tracks/68/loop_wrap = true
tracks/68/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-1493, 3040), Vector2(-1493, 3040), Vector2(3139.75, 3040), Vector2(-1493, 3040)]
}
tracks/69/type = "value"
tracks/69/imported = false
tracks/69/enabled = true
tracks/69/path = NodePath("Environment/Clouds/Cloud23:scale")
tracks/69/interp = 1
tracks/69/loop_wrap = true
tracks/69/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/70/type = "value"
tracks/70/imported = false
tracks/70/enabled = true
tracks/70/path = NodePath("Environment/Clouds/Cloud24:position")
tracks/70/interp = 1
tracks/70/loop_wrap = true
tracks/70/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-510, 2429), Vector2(-510, 2429), Vector2(4122.75, 2429), Vector2(-510, 2429)]
}
tracks/71/type = "value"
tracks/71/imported = false
tracks/71/enabled = true
tracks/71/path = NodePath("Environment/Clouds/Cloud24:scale")
tracks/71/interp = 1
tracks/71/loop_wrap = true
tracks/71/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/72/type = "value"
tracks/72/imported = false
tracks/72/enabled = true
tracks/72/path = NodePath("Environment/Clouds/Cloud25:position")
tracks/72/interp = 1
tracks/72/loop_wrap = true
tracks/72/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-712, 1373), Vector2(-712, 1373), Vector2(3920.75, 1373), Vector2(-712, 1373)]
}
tracks/73/type = "value"
tracks/73/imported = false
tracks/73/enabled = true
tracks/73/path = NodePath("Environment/Clouds/Cloud25:scale")
tracks/73/interp = 1
tracks/73/loop_wrap = true
tracks/73/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/74/type = "value"
tracks/74/imported = false
tracks/74/enabled = true
tracks/74/path = NodePath("Environment/Clouds/Cloud26:position")
tracks/74/interp = 1
tracks/74/loop_wrap = true
tracks/74/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-269, 2004), Vector2(-269, 2004), Vector2(4021.5, 2004), Vector2(-269, 2004)]
}
tracks/75/type = "value"
tracks/75/imported = false
tracks/75/enabled = true
tracks/75/path = NodePath("Environment/Clouds/Cloud26:scale")
tracks/75/interp = 1
tracks/75/loop_wrap = true
tracks/75/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(0.9, 0.9), Vector2(1, 1)]
}
tracks/76/type = "value"
tracks/76/imported = false
tracks/76/enabled = true
tracks/76/path = NodePath("Environment/Clouds/Cloud27:position")
tracks/76/interp = 1
tracks/76/loop_wrap = true
tracks/76/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-1656, 2353), Vector2(-1656, 2353), Vector2(2634.5, 2353), Vector2(-1656, 2353)]
}
tracks/77/type = "value"
tracks/77/imported = false
tracks/77/enabled = true
tracks/77/path = NodePath("Environment/Clouds/Cloud27:scale")
tracks/77/interp = 1
tracks/77/loop_wrap = true
tracks/77/keys = {
"times": PackedFloat32Array(0, 30, 56, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(0.9, 0.9), Vector2(1, 1)]
}
tracks/78/type = "value"
tracks/78/imported = false
tracks/78/enabled = true
tracks/78/path = NodePath("Environment/Clouds/Cloud23:visible")
tracks/78/interp = 1
tracks/78/loop_wrap = true
tracks/78/keys = {
"times": PackedFloat32Array(0, 56, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/79/type = "value"
tracks/79/imported = false
tracks/79/enabled = true
tracks/79/path = NodePath("Environment/Clouds/Cloud24:visible")
tracks/79/interp = 1
tracks/79/loop_wrap = true
tracks/79/keys = {
"times": PackedFloat32Array(0, 56, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/80/type = "value"
tracks/80/imported = false
tracks/80/enabled = true
tracks/80/path = NodePath("Environment/Clouds/Cloud25:visible")
tracks/80/interp = 1
tracks/80/loop_wrap = true
tracks/80/keys = {
"times": PackedFloat32Array(0, 56, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/81/type = "value"
tracks/81/imported = false
tracks/81/enabled = true
tracks/81/path = NodePath("Environment/Clouds/Cloud26:visible")
tracks/81/interp = 1
tracks/81/loop_wrap = true
tracks/81/keys = {
"times": PackedFloat32Array(0, 56, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/82/type = "value"
tracks/82/imported = false
tracks/82/enabled = true
tracks/82/path = NodePath("Environment/Clouds/Cloud27:visible")
tracks/82/interp = 1
tracks/82/loop_wrap = true
tracks/82/keys = {
"times": PackedFloat32Array(0, 56, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/83/type = "value"
tracks/83/imported = false
tracks/83/enabled = true
tracks/83/path = NodePath("Environment/Clouds/Cloud28:visible")
tracks/83/interp = 1
tracks/83/loop_wrap = true
tracks/83/keys = {
"times": PackedFloat32Array(0, 56, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/84/type = "value"
tracks/84/imported = false
tracks/84/enabled = true
tracks/84/path = NodePath("Environment/Clouds/Cloud29:position")
tracks/84/interp = 1
tracks/84/loop_wrap = true
tracks/84/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(3890, 2612.5), Vector2(7216.5, 2622.5), Vector2(3890, 2612.5)]
}
tracks/85/type = "value"
tracks/85/imported = false
tracks/85/enabled = true
tracks/85/path = NodePath("Environment/Clouds/Cloud29:scale")
tracks/85/interp = 1
tracks/85/loop_wrap = true
tracks/85/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/86/type = "value"
tracks/86/imported = false
tracks/86/enabled = true
tracks/86/path = NodePath("Environment/Clouds/Cloud30:position")
tracks/86/interp = 1
tracks/86/loop_wrap = true
tracks/86/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(3832, 1394.5), Vector2(7158.5, 1404.5), Vector2(3832, 1394.5)]
}
tracks/87/type = "value"
tracks/87/imported = false
tracks/87/enabled = true
tracks/87/path = NodePath("Environment/Clouds/Cloud30:scale")
tracks/87/interp = 1
tracks/87/loop_wrap = true
tracks/87/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/88/type = "value"
tracks/88/imported = false
tracks/88/enabled = true
tracks/88/path = NodePath("Environment/Clouds/Cloud31:position")
tracks/88/interp = 1
tracks/88/loop_wrap = true
tracks/88/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(3472, 3106), Vector2(6798.5, 3116), Vector2(3472, 3106)]
}
tracks/89/type = "value"
tracks/89/imported = false
tracks/89/enabled = true
tracks/89/path = NodePath("Environment/Clouds/Cloud31:scale")
tracks/89/interp = 1
tracks/89/loop_wrap = true
tracks/89/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/90/type = "value"
tracks/90/imported = false
tracks/90/enabled = true
tracks/90/path = NodePath("Environment/Clouds/Cloud32:position")
tracks/90/interp = 1
tracks/90/loop_wrap = true
tracks/90/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(3095, 2689.5), Vector2(6421.5, 2699.5), Vector2(3095, 2689.5)]
}
tracks/91/type = "value"
tracks/91/imported = false
tracks/91/enabled = true
tracks/91/path = NodePath("Environment/Clouds/Cloud32:scale")
tracks/91/interp = 1
tracks/91/loop_wrap = true
tracks/91/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/92/type = "value"
tracks/92/imported = false
tracks/92/enabled = true
tracks/92/path = NodePath("Environment/Clouds/Cloud33:position")
tracks/92/interp = 1
tracks/92/loop_wrap = true
tracks/92/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(4016, 1558.5), Vector2(7342.5, 1568.5), Vector2(4016, 1558.5)]
}
tracks/93/type = "value"
tracks/93/imported = false
tracks/93/enabled = true
tracks/93/path = NodePath("Environment/Clouds/Cloud33:scale")
tracks/93/interp = 1
tracks/93/loop_wrap = true
tracks/93/keys = {
"times": PackedFloat32Array(0, 20, 60),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/94/type = "value"
tracks/94/imported = false
tracks/94/enabled = true
tracks/94/path = NodePath("Environment/Clouds/Cloud29:visible")
tracks/94/interp = 1
tracks/94/loop_wrap = true
tracks/94/keys = {
"times": PackedFloat32Array(0, 20, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/95/type = "value"
tracks/95/imported = false
tracks/95/enabled = true
tracks/95/path = NodePath("Environment/Clouds/Cloud30:visible")
tracks/95/interp = 1
tracks/95/loop_wrap = true
tracks/95/keys = {
"times": PackedFloat32Array(0, 20, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/96/type = "value"
tracks/96/imported = false
tracks/96/enabled = true
tracks/96/path = NodePath("Environment/Clouds/Cloud31:visible")
tracks/96/interp = 1
tracks/96/loop_wrap = true
tracks/96/keys = {
"times": PackedFloat32Array(0, 20, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/97/type = "value"
tracks/97/imported = false
tracks/97/enabled = true
tracks/97/path = NodePath("Environment/Clouds/Cloud32:visible")
tracks/97/interp = 1
tracks/97/loop_wrap = true
tracks/97/keys = {
"times": PackedFloat32Array(0, 20, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/98/type = "value"
tracks/98/imported = false
tracks/98/enabled = true
tracks/98/path = NodePath("Environment/Clouds/Cloud33:visible")
tracks/98/interp = 1
tracks/98/loop_wrap = true
tracks/98/keys = {
"times": PackedFloat32Array(0, 20, 60.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/99/type = "value"
tracks/99/imported = false
tracks/99/enabled = true
tracks/99/path = NodePath("Environment/Clouds/Cloud34:position")
tracks/99/interp = 1
tracks/99/loop_wrap = true
tracks/99/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(3863, 1375), Vector2(3863, 1375), Vector2(7431, 1375), Vector2(3863, 1375)]
}
tracks/100/type = "value"
tracks/100/imported = false
tracks/100/enabled = true
tracks/100/path = NodePath("Environment/Clouds/Cloud34:scale")
tracks/100/interp = 1
tracks/100/loop_wrap = true
tracks/100/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/101/type = "value"
tracks/101/imported = false
tracks/101/enabled = true
tracks/101/path = NodePath("Environment/Clouds/Cloud35:position")
tracks/101/interp = 1
tracks/101/loop_wrap = true
tracks/101/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(3521.25, 3087), Vector2(3521.25, 3087), Vector2(7089.25, 3087), Vector2(3521.25, 3087)]
}
tracks/102/type = "value"
tracks/102/imported = false
tracks/102/enabled = true
tracks/102/path = NodePath("Environment/Clouds/Cloud35:scale")
tracks/102/interp = 1
tracks/102/loop_wrap = true
tracks/102/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/103/type = "value"
tracks/103/imported = false
tracks/103/enabled = true
tracks/103/path = NodePath("Environment/Clouds/Cloud36:position")
tracks/103/interp = 1
tracks/103/loop_wrap = true
tracks/103/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(3609.25, 3161), Vector2(3609.25, 3161), Vector2(7177.25, 3161), Vector2(3609.25, 3161)]
}
tracks/104/type = "value"
tracks/104/imported = false
tracks/104/enabled = true
tracks/104/path = NodePath("Environment/Clouds/Cloud36:scale")
tracks/104/interp = 1
tracks/104/loop_wrap = true
tracks/104/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/105/type = "value"
tracks/105/imported = false
tracks/105/enabled = true
tracks/105/path = NodePath("Environment/Clouds/Cloud37:position")
tracks/105/interp = 1
tracks/105/loop_wrap = true
tracks/105/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(3754.25, 1447), Vector2(3754.25, 1447), Vector2(7322.25, 1447), Vector2(3754.25, 1447)]
}
tracks/106/type = "value"
tracks/106/imported = false
tracks/106/enabled = true
tracks/106/path = NodePath("Environment/Clouds/Cloud37:scale")
tracks/106/interp = 1
tracks/106/loop_wrap = true
tracks/106/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/107/type = "value"
tracks/107/imported = false
tracks/107/enabled = true
tracks/107/path = NodePath("Environment/Clouds/Cloud38:position")
tracks/107/interp = 1
tracks/107/loop_wrap = true
tracks/107/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(2915.25, 2833), Vector2(2915.25, 2833), Vector2(6483.25, 2833), Vector2(2915.25, 2833)]
}
tracks/108/type = "value"
tracks/108/imported = false
tracks/108/enabled = true
tracks/108/path = NodePath("Environment/Clouds/Cloud38:scale")
tracks/108/interp = 1
tracks/108/loop_wrap = true
tracks/108/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(0.898305, 0.898305), Vector2(0.898305, 0.898305), Vector2(1.3, 1.3), Vector2(0.898305, 0.898305)]
}
tracks/109/type = "value"
tracks/109/imported = false
tracks/109/enabled = true
tracks/109/path = NodePath("Environment/Clouds/Cloud39:position")
tracks/109/interp = 1
tracks/109/loop_wrap = true
tracks/109/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(3270.25, 2207), Vector2(3270.25, 2207), Vector2(6838.25, 2207), Vector2(3270.25, 2207)]
}
tracks/110/type = "value"
tracks/110/imported = false
tracks/110/enabled = true
tracks/110/path = NodePath("Environment/Clouds/Cloud39:scale")
tracks/110/interp = 1
tracks/110/loop_wrap = true
tracks/110/keys = {
"times": PackedFloat32Array(0, 22, 35, 60),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.3, 1.3), Vector2(1, 1)]
}
tracks/111/type = "value"
tracks/111/imported = false
tracks/111/enabled = true
tracks/111/path = NodePath("Environment/Clouds/Cloud34:visible")
tracks/111/interp = 1
tracks/111/loop_wrap = true
tracks/111/keys = {
"times": PackedFloat32Array(0, 22, 35, 60.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [true, true, false, true]
}
tracks/112/type = "value"
tracks/112/imported = false
tracks/112/enabled = true
tracks/112/path = NodePath("Environment/Clouds/Cloud35:visible")
tracks/112/interp = 1
tracks/112/loop_wrap = true
tracks/112/keys = {
"times": PackedFloat32Array(0, 22, 35, 60.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [true, true, false, true]
}
tracks/113/type = "value"
tracks/113/imported = false
tracks/113/enabled = true
tracks/113/path = NodePath("Environment/Clouds/Cloud36:visible")
tracks/113/interp = 1
tracks/113/loop_wrap = true
tracks/113/keys = {
"times": PackedFloat32Array(0, 22, 35, 60.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [true, true, false, true]
}
tracks/114/type = "value"
tracks/114/imported = false
tracks/114/enabled = true
tracks/114/path = NodePath("Environment/Clouds/Cloud37:visible")
tracks/114/interp = 1
tracks/114/loop_wrap = true
tracks/114/keys = {
"times": PackedFloat32Array(0, 22, 35, 60.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [true, true, false, true]
}
tracks/115/type = "value"
tracks/115/imported = false
tracks/115/enabled = true
tracks/115/path = NodePath("Environment/Clouds/Cloud38:visible")
tracks/115/interp = 1
tracks/115/loop_wrap = true
tracks/115/keys = {
"times": PackedFloat32Array(0, 22, 35, 60.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [true, true, false, true]
}
tracks/116/type = "value"
tracks/116/imported = false
tracks/116/enabled = true
tracks/116/path = NodePath("Environment/Clouds/Cloud39:visible")
tracks/116/interp = 1
tracks/116/loop_wrap = true
tracks/116/keys = {
"times": PackedFloat32Array(0, 22, 35, 60.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [true, true, false, true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_an3cu"]
_data = {
&"RESET": SubResource("Animation_gwtgs"),
&"float": SubResource("Animation_75lmk")
}

[node name="Arena" type="Node2D"]
y_sort_enabled = true
metadata/_edit_vertical_guides_ = [962.0]
metadata/_edit_horizontal_guides_ = [541.0]

[node name="Environment" type="Node2D" parent="."]
top_level = true
z_index = -1
metadata/_edit_lock_ = true

[node name="Sky" type="ColorRect" parent="Environment"]
custom_minimum_size = Vector2(6000, 4000)
offset_left = -2048.0
offset_top = -1498.0
offset_right = 3952.0
offset_bottom = 2502.0
color = Color(0.537255, 0.745098, 0.854902, 1)
metadata/_edit_lock_ = true

[node name="Clouds" type="Node2D" parent="Environment"]
modulate = Color(1, 1, 1, 0.792157)
position = Vector2(-1998, -1508)
metadata/_edit_lock_ = true

[node name="Cloud1" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(764, 358)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud13" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(4005, 2983)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud5" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-152, 2723)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud6" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3293, 936)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud2" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(85, 1505)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud16" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3434, 3542)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud7" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(1380, 3802)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud3" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(503, 1222)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud14" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(4174, 3309)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud15" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(4695, 2800)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud8" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(152, 3383)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud4" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(164, 1669)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud9" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(328, 3474)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud10" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(933, 515)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud12" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(4570, 1222)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud11" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3784, 345)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud17" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-2737.13, 367.814)
scale = Vector2(1.35085, 1.15254)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud18" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-1254.92, 3539.61)
scale = Vector2(0.898305, 0.898305)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud19" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-2912.7, 1260.29)
scale = Vector2(1.10169, 1.10169)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud20" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-3251.5, 3437.56)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud21" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-3181.26, 3581.44)
scale = Vector2(0.949153, 0.949153)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud22" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-420.856, 367.017)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud23" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-1493, 3040)
scale = Vector2(0.898305, 0.898305)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud24" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-510, 2429)
scale = Vector2(1.15254, 1.15254)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud25" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-712, 1373)
scale = Vector2(1.10169, 1.10169)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud26" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-269, 2004)
scale = Vector2(0.79661, 0.79661)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud27" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-1656, 2353)
scale = Vector2(0.949153, 0.949153)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud28" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(-461, 1787)
scale = Vector2(1.10169, 1.10169)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud29" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3890, 2612.5)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud30" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3832, 1394.5)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud31" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3472, 3106)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud32" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3095, 2689.5)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud33" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(4016, 1558.5)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud34" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3863, 1375)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(12, 7, 455, 168)

[node name="Cloud35" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3521.25, 3087)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud36" type="Sprite2D" parent="Environment/Clouds"]
position = Vector2(3609.25, 3161)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(507, 7, 193, 88)

[node name="Cloud37" type="Sprite2D" parent="Environment/Clouds"]
visible = false
position = Vector2(3754.25, 1447)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(501, 99, 210, 92)

[node name="Cloud38" type="Sprite2D" parent="Environment/Clouds"]
visible = false
position = Vector2(2915.25, 2833)
scale = Vector2(0.898305, 0.898305)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="Cloud39" type="Sprite2D" parent="Environment/Clouds"]
visible = false
position = Vector2(3270.25, 2207)
texture = ExtResource("1_1blat")
region_enabled = true
region_rect = Rect2(10, 189, 644, 165)

[node name="CloudsAnimationPlayer" type="AnimationPlayer" parent="Environment"]
root_node = NodePath("../..")
libraries = {
&"": SubResource("AnimationLibrary_an3cu")
}
autoplay = "float"

[node name="Ground" type="Node2D" parent="Environment"]
metadata/_edit_lock_ = true

[node name="Rocks" type="Sprite2D" parent="Environment/Ground"]
position = Vector2(986, 1264)
texture = ExtResource("1_145kx")

[node name="Floor" type="Sprite2D" parent="Environment/Ground"]
position = Vector2(938, 568)
texture = ExtResource("1_vifjc")

[node name="Limits" type="StaticBody2D" parent="Environment"]
metadata/_edit_lock_ = true

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="Environment/Limits"]
visible = false
polygon = PackedVector2Array(-814, 535, -380, 190, -306, -21, 87, -46, 346, -330, 870, -373, 1115, -418, 1363, -334, 1613, -324, 1854, -225, 2208, -197, 2675, 309, 2642, 741, 2244, 1140, 1618, 1271, 1360, 1193, 865, 1423, 706, 1377, -113, 1192, -452, 1035, -488, 902, -625, 803, -734, 680, -1660, 719, -1331, 2205, 3170, 2304, 3988, 404, 2999, -1433, 131, -1364, -1715, 264, -1659, 716, -737, 676)
metadata/_edit_lock_ = true

[node name="Props" type="Node2D" parent="."]
y_sort_enabled = true
metadata/_edit_lock_ = true

[node name="Shrooms" type="Node2D" parent="Props"]
y_sort_enabled = true
metadata/_edit_lock_ = true

[node name="ShroomFat1" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(316, -293)
rotation = -0.47206
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat3" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(889.983, -375.68)
rotation = 0.0723415
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomRed1" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(211, -172)
rotation = -0.554414
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed2" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(477, -354)
rotation = -0.131447
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed3" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1069, -418)
rotation = 0.210047
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed4" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1184, -390)
rotation = -0.111158
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed5" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1522, -323)
rotation = -0.111158
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed6" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1776, -257)
rotation = 0.0517097
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed7" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2002, -212)
rotation = -0.0711099
scale = Vector2(1.26843, 1.26843)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomBlue1" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-18, -43)
rotation = -0.390338
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue2" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(653, -364)
rotation = -0.390338
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue3" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1251, -379)
rotation = -0.0470768
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue4" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1357, -339)
rotation = 0.382315
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue5" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2188, -215)
rotation = 0.177698
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomThinFlip1" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(451.932, -348.31)
rotation = -3.01653
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThinFlip2" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1324, -351)
rotation = -2.73344
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThinFlip3" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2151.93, -234.31)
rotation = -3.0381
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomFat2" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1901.98, -226.68)
rotation = -0.202467
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomYellow1" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(53, -53)
rotation = 0.0837674
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow2" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(283, -266)
rotation = -0.486485
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow4" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1009, -410)
rotation = 0.0837674
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow5" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1632, -326)
rotation = -0.179329
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow6" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1668, -313)
rotation = 0.49724
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow7" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2082, -232)
rotation = -0.300902
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomThin2" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-112.5, -30.0442)
rotation = -0.0993826
scale = Vector2(0.762573, 0.762573)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin3" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(104, -70)
rotation = 0.0063462
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin4" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(202.5, -223.044)
rotation = -0.108585
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin5" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(381.5, -325.044)
rotation = -0.027
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin6" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(542.5, -367.044)
rotation = -0.027
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin7" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(790.5, -372.044)
rotation = 0.336266
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin8" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1266, -377)
rotation = 0.481942
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin9" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1436.5, -326.044)
rotation = -0.0734592
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin10" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1666.5, -292.044)
rotation = -0.106563
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomRed8" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2289, -167)
rotation = 0.0517097
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomThin1" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-122, -58)
rotation = 0.434625
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin11" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2088.5, -233.044)
rotation = -0.0820285
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin12" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-800, 471)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin13" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-457, 62)
rotation = -0.368511
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin14" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-350.5, -23.0442)
rotation = 0.348356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThinFlip4" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-660.068, 388.69)
rotation = -3.01653
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomFat4" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-819, 547)
rotation = -0.338786
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat5" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-510, 252)
rotation = -0.121513
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat6" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-192, -54)
rotation = -0.182008
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomBlue6" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-713, 715)
rotation = -0.434928
scale = Vector2(1.11803, 1.02553)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue7" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-724, 448)
rotation = 0.0185129
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue8" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-405, -1.00002)
rotation = 0.0185129
scale = Vector2(1.08529, 0.99636)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomRed9" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-725, 776)
rotation = -0.737596
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomYellow3" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-601, 352)
rotation = 0.0245847
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow8" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-394, 213)
rotation = 0.304539
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow9" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-297, -12)
rotation = 0.0245847
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomThin15" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-472, 2)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomYellow10" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2302, -93.9999)
rotation = 0.443771
scale = Vector2(1.48644, 1.42174)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomFat7" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2673, 269)
rotation = 0.249999
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat8" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2408.98, -10.6802)
rotation = 0.249999
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomRed10" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2673, 603)
rotation = -0.205529
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed11" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2457, 62.9999)
rotation = 0.133289
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomThinFlip5" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2758.93, 383.69)
rotation = -2.72375
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomYellow11" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2680, 496)
rotation = 0.443771
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow12" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2502, 122)
rotation = 0.443771
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomRed12" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2558, 184)
rotation = 0.439175
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomBlue9" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2721, 315)
rotation = 0.0185129
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomThin16" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2610, 225)
rotation = -0.321984
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin17" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2387.5, 11.9558)
rotation = -0.0440143
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomRed13" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-556, 849)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed14" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(770, 1513)
rotation = 0.159869
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed15" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1349, 1241)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed16" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1261, 1284)
rotation = -0.542647
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed17" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2312, 1116)
rotation = -0.294691
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomRed18" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2508, 864)
rotation = -0.259673
texture = ExtResource("2_kesm7")
offset = Vector2(0, -124)
region_enabled = true
region_rect = Rect2(667.382, 0, 217.218, 259.018)

[node name="ShroomYellow13" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-503, 876)
rotation = 0.218248
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow14" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(58, 1208)
rotation = 0.116333
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow15" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(529, 1365)
rotation = 0.0404178
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow16" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(934, 1419)
rotation = -0.0691034
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow17" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1605.04, 1336.65)
rotation = -0.0585388
scale = Vector2(1, 1.00276)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow18" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1943, 1282)
rotation = 0.0341268
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomYellow19" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2631, 819)
rotation = -0.0863327
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomFat9" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-379, 1132)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomThinFlip6" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-583, 1039)
rotation = -2.70279
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThinFlip7" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(168.932, 1262.69)
rotation = -2.70279
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThinFlip8" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(903.932, 1420.69)
rotation = -3.09863
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThinFlip9" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1684.93, 1303.69)
rotation = -3.09863
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin18" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-277.5, 1192.96)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin19" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(104.5, 1254.96)
rotation = 0.328221
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin20" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(673, 1509)
rotation = 0.388862
scale = Vector2(0.799106, 0.799106)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomFat10" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-683.017, 859.32)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat11" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(357.983, 1353.32)
rotation = -0.210709
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat12" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1047, 1369)
rotation = 0.0773245
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat13" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1796.98, 1283.32)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomFat14" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2416, 1002)
rotation = -0.543407
texture = ExtResource("2_kesm7")
offset = Vector2(0, -118)
region_enabled = true
region_rect = Rect2(364.026, 0, 303.355, 246.522)

[node name="ShroomThin21" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(446.5, 1378.96)
rotation = 0.388862
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin22" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1842, 1288)
rotation = 0.47074
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin23" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2473, 905)
rotation = -0.509121
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin24" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2570.5, 827.956)
rotation = -0.0398957
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomBlue10" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-80, 1198)
rotation = -0.110138
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue11" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-231, 1209)
rotation = 0.335187
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue12" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(638, 1380)
rotation = -0.110138
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue13" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1454, 1249)
rotation = -0.110138
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue14" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2203, 1198)
rotation = -0.110138
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue15" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2100, 1241)
rotation = -0.440792
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomBlue16" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2712, 770)
rotation = -0.0419129
scale = Vector2(1.27499, 1.17746)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -115)
region_enabled = true
region_rect = Rect2(175.8, 0, 188.1, 246.522)

[node name="ShroomThinFlip10" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2238.93, 1162.69)
rotation = 2.58643
scale = Vector2(0.729666, -0.765873)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin25" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(2136.5, 1225.96)
rotation = -0.0459579
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin26" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(1512, 1310)
rotation = -0.223667
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomThin27" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-12.5, 1227.96)
rotation = -0.194346
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomYellow20" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-577, 977)
rotation = -0.196464
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="ShroomThin28" type="Sprite2D" parent="Props/Shrooms"]
position = Vector2(-645, 1030)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -110)
region_enabled = true
region_rect = Rect2(886.896, 0, 99.9249, 222.903)

[node name="ShroomYellow21" type="Sprite2D" parent="Props/Shrooms"]
y_sort_enabled = true
position = Vector2(731, -360)
rotation = 0.0837674
texture = ExtResource("2_kesm7")
offset = Vector2(0, -116)
region_enabled = true
region_rect = Rect2(-0.296913, 2.76804, 172.051, 239.67)

[node name="Grass" type="Node2D" parent="Props"]
y_sort_enabled = true
metadata/_edit_lock_ = true

[node name="Grass1" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-438, -25)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass2" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-356, -28)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass3" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-210, -64)
rotation = -0.191355
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass4" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-279, -60)
rotation = -0.351512
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass5" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-140, -66)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass6" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-76, -63)
rotation = 0.150085
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass7" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-6, -64)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass8" type="Sprite2D" parent="Props/Grass"]
position = Vector2(51, -70)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass9" type="Sprite2D" parent="Props/Grass"]
position = Vector2(105, -82)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass10" type="Sprite2D" parent="Props/Grass"]
position = Vector2(162, -106)
rotation = -0.447914
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass11" type="Sprite2D" parent="Props/Grass"]
position = Vector2(178, -174)
rotation = -0.826729
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass12" type="Sprite2D" parent="Props/Grass"]
position = Vector2(209, -232)
rotation = -0.708204
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass13" type="Sprite2D" parent="Props/Grass"]
position = Vector2(241, -276)
rotation = -0.614925
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass14" type="Sprite2D" parent="Props/Grass"]
position = Vector2(334, -323)
rotation = -0.476317
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass15" type="Sprite2D" parent="Props/Grass"]
position = Vector2(385, -352)
rotation = -0.3282
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass16" type="Sprite2D" parent="Props/Grass"]
position = Vector2(441, -376)
rotation = -0.219466
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass17" type="Sprite2D" parent="Props/Grass"]
position = Vector2(513, -388)
rotation = -0.0260885
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass18" type="Sprite2D" parent="Props/Grass"]
position = Vector2(571, -394)
rotation = 0.123957
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass19" type="Sprite2D" parent="Props/Grass"]
position = Vector2(627.224, -381.064)
rotation = 0.0929263
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass20" type="Sprite2D" parent="Props/Grass"]
position = Vector2(692, -382)
rotation = 0.10344
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass21" type="Sprite2D" parent="Props/Grass"]
position = Vector2(760, -376)
rotation = 0.000137895
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass22" type="Sprite2D" parent="Props/Grass"]
position = Vector2(813, -380)
rotation = 0.193515
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass23" type="Sprite2D" parent="Props/Grass"]
position = Vector2(863, -394)
rotation = 0.0942186
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass24" type="Sprite2D" parent="Props/Grass"]
position = Vector2(953, -417)
rotation = -0.108891
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass25" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1025, -436)
rotation = -0.219926
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass26" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1090, -444)
rotation = -0.0691996
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass27" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1148, -438)
rotation = 0.0942186
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass28" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1206, -419)
rotation = 0.325109
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass29" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1288, -387)
rotation = 0.372396
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass30" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1341, -367)
rotation = 0.4261
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass31" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1392.22, -349.064)
rotation = 0.248156
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass32" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1470, -345)
rotation = 0.140579
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass33" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1537, -344)
rotation = 0.151143
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass34" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1603, -342)
rotation = 0.101008
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass35" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1659, -336)
rotation = 0.248156
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass36" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1719, -319)
rotation = 0.28446
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass37" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1781.22, -292.064)
rotation = 0.440044
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass38" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1842.22, -257.064)
rotation = 0.352407
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass39" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1915, -248)
rotation = 0.342354
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass40" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1987, -236)
rotation = 0.0942217
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass41" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2063, -246)
rotation = 0.0141966
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass42" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2133, -247)
rotation = 0.107299
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass43" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2205, -237)
rotation = 0.352407
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass44" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2271.22, -219.064)
rotation = 0.342354
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass45" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2301, -137)
rotation = 0.940869
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass46" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2351, -42)
rotation = 0.458519
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass47" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2442, -4)
rotation = 0.3067
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass48" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2491, 36.0001)
rotation = 0.534738
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass49" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2517.19, 77.2042)
rotation = 0.830507
scale = Vector2(1.00312, 1)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass50" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2575, 147)
rotation = 0.976222
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass51" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2552, 127)
rotation = 0.668316
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass52" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2618, 199)
rotation = 0.534738
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass53" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2711, 264)
rotation = 0.830507
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass54" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-368, 174)
rotation = -0.456926
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass55" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-443, 208)
rotation = -0.199051
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass56" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-496, 229)
rotation = -0.456926
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass57" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-593, 303)
rotation = -0.456926
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass58" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-652, 357)
rotation = -0.456926
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass59" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-698.776, 402.936)
rotation = -0.456926
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass60" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-771, 447)
rotation = -0.456926
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass61" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-801, 488)
rotation = -0.986285
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass62" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2713, 523)
rotation = -0.236838
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass63" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2723, 333)
rotation = -0.0377601
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass74" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-528, 883)
rotation = 0.425283
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass75" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-567, 879)
rotation = -0.145538
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass76" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-618, 878)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass77" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-702, 875)
rotation = -0.136894
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass78" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-640, 870)
rotation = 0.0992107
scale = Vector2(0.89227, 0.890196)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass73" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2726, 803)
rotation = -0.177835
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass72" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2687.59, 820.894)
rotation = -0.335356
scale = Vector2(0.996004, 0.997321)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass71" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2635, 831)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass70" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2584, 850)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass69" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2534, 878)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass68" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2485, 937)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass67" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2472, 984)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass66" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2442, 1015)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass65" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2404, 1033)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass64" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2357, 1063)
rotation = -0.419734
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass79" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2328, 1128)
rotation = -0.449816
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass80" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2321, 1180)
rotation = -0.194528
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass81" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2309, 1201)
rotation = -0.24821
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass82" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2283, 1232)
rotation = -0.335356
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass83" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2231, 1246)
rotation = -0.0390497
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass84" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2172, 1251)
rotation = -0.0459226
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass85" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2116, 1252)
rotation = -0.140489
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass86" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2062, 1263)
rotation = -0.295208
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass87" type="Sprite2D" parent="Props/Grass"]
position = Vector2(2011, 1282)
rotation = -0.261242
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass88" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1964, 1309)
rotation = -0.138799
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass89" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1906, 1315)
rotation = 0.0341306
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass90" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1857, 1309)
rotation = -0.00321719
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass91" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1795, 1307)
rotation = -0.0304235
scale = Vector2(0.994353, 0.99967)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass92" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1639, 1358)
rotation = -0.250016
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass93" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1563, 1353)
rotation = 0.257884
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass94" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1739, 1322)
rotation = -0.22541
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass95" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1691, 1341)
rotation = -0.244855
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass96" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1506, 1318)
rotation = 0.717251
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass97" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1466, 1276)
rotation = 0.644911
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass98" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1425, 1259)
rotation = 0.0565641
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass99" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1366, 1265)
rotation = -0.149705
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass100" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1308, 1292)
rotation = -0.153005
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass101" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1255, 1311)
rotation = 0.0565641
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass102" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1137, 1337)
rotation = 0.0565641
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass103" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1096, 1376)
rotation = -0.369265
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass104" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1051, 1418)
rotation = -0.358503
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass105" type="Sprite2D" parent="Props/Grass"]
position = Vector2(947, 1450)
rotation = -0.124099
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass106" type="Sprite2D" parent="Props/Grass"]
position = Vector2(988, 1444)
rotation = 0.0565641
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass107" type="Sprite2D" parent="Props/Grass"]
position = Vector2(1203, 1319)
rotation = -0.10731
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass108" type="Sprite2D" parent="Props/Grass"]
position = Vector2(851, 1493)
rotation = -0.241176
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass109" type="Sprite2D" parent="Props/Grass"]
position = Vector2(894, 1462)
rotation = -0.0791258
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass110" type="Sprite2D" parent="Props/Grass"]
position = Vector2(823, 1532)
rotation = -0.36178
scale = Vector2(0.896039, 0.929433)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass111" type="Sprite2D" parent="Props/Grass"]
position = Vector2(778, 1556)
rotation = -0.183817
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass112" type="Sprite2D" parent="Props/Grass"]
position = Vector2(711, 1555)
rotation = 0.23761
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass113" type="Sprite2D" parent="Props/Grass"]
position = Vector2(661, 1543)
rotation = 0.23761
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass114" type="Sprite2D" parent="Props/Grass"]
position = Vector2(678, 1421)
rotation = 0.450015
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass115" type="Sprite2D" parent="Props/Grass"]
position = Vector2(653, 1408)
rotation = 0.246509
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass116" type="Sprite2D" parent="Props/Grass"]
position = Vector2(603, 1405)
rotation = -0.0914229
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass117" type="Sprite2D" parent="Props/Grass"]
position = Vector2(547, 1414)
rotation = -0.119099
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass118" type="Sprite2D" parent="Props/Grass"]
position = Vector2(489, 1402)
rotation = 0.450015
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass119" type="Sprite2D" parent="Props/Grass"]
position = Vector2(436, 1385)
rotation = 0.305517
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass120" type="Sprite2D" parent="Props/Grass"]
position = Vector2(395, 1383)
rotation = -0.203084
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass121" type="Sprite2D" parent="Props/Grass"]
position = Vector2(352, 1390)
rotation = -0.194154
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass122" type="Sprite2D" parent="Props/Grass"]
position = Vector2(257, 1351)
rotation = 0.488442
scale = Vector2(0.984626, 0.967626)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass123" type="Sprite2D" parent="Props/Grass"]
position = Vector2(288, 1385)
rotation = 0.450015
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass124" type="Sprite2D" parent="Props/Grass"]
position = Vector2(221, 1329)
rotation = 0.132079
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass125" type="Sprite2D" parent="Props/Grass"]
position = Vector2(174, 1314)
rotation = 0.346821
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass126" type="Sprite2D" parent="Props/Grass"]
position = Vector2(133, 1299)
rotation = 0.450015
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass127" type="Sprite2D" parent="Props/Grass"]
position = Vector2(88, 1271)
rotation = 0.450015
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass128" type="Sprite2D" parent="Props/Grass"]
position = Vector2(44.224, 1260.94)
rotation = 0.0963627
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass129" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-4.776, 1248.94)
rotation = 0.0963627
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass130" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-52.776, 1239.94)
rotation = 0.0963627
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass131" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-113, 1228)
rotation = 0.0963627
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass132" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-168, 1233)
rotation = -0.0568394
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass133" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-225, 1238)
rotation = 0.0963627
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass134" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-281, 1225)
rotation = 0.599394
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass135" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-313, 1160)
rotation = -0.539764
scale = Vector2(0.920388, 0.98772)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass136" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-366, 1176)
rotation = 0.109422
scale = Vector2(0.936615, 0.872008)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass137" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-423, 1151)
rotation = 0.527011
scale = Vector2(0.824352, 0.816612)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass138" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-471, 1133)
rotation = 0.452393
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass139" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-541, 1058)
rotation = 0.138139
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass140" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-590, 1054)
rotation = -0.110402
scale = Vector2(0.845736, 0.853483)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)

[node name="Grass141" type="Sprite2D" parent="Props/Grass"]
position = Vector2(-642, 1052)
texture = ExtResource("2_kesm7")
offset = Vector2(0, -42)
region_enabled = true
region_rect = Rect2(996.053, 4.65938, 177.044, 92.8809)
