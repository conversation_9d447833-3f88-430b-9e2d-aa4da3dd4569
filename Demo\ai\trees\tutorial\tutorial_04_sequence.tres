[gd_resource type="BehaviorTree" load_steps=15 format=3 uid="uid://dln8ywvtqedt7"]

[ext_resource type="Script" uid="uid://bicxffqmm7ek" path="res://demo/ai/tasks/select_random_nearby_pos.gd" id="1_gk66j"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="2_v5eou"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_ewfwq"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_fq0jf"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_rsejo"]
animation_player = SubResource("BBNode_fq0jf")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_dl31p"]
duration = 3.0

[sub_resource type="BBNode" id="BBNode_6d0yy"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_wsspf"]
animation_player = SubResource("BBNode_6d0yy")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_fc1oo"]
script = ExtResource("1_gk66j")
range_min = 300.0
range_max = 500.0
position_var = &"pos"

[sub_resource type="BTAction" id="BTAction_djl5v"]
script = ExtResource("2_v5eou")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTFail" id="BTFail_i2f36"]

[sub_resource type="BTComment" id="BTComment_4i06i"]
custom_name = "👇️ Sequence is never going to reach this task, because it fails at the previous step."

[sub_resource type="BTWait" id="BTWait_yxpnx"]
duration = 888.0

[sub_resource type="BTSequence" id="BTSequence_vx4uy"]
children = [SubResource("BTPlayAnimation_rsejo"), SubResource("BTWait_dl31p"), SubResource("BTPlayAnimation_wsspf"), SubResource("BTAction_fc1oo"), SubResource("BTAction_djl5v"), SubResource("BTFail_i2f36"), SubResource("BTComment_4i06i"), SubResource("BTWait_yxpnx")]

[resource]
description = "The [comp]Sequence[/comp] is one of the core composite tasks. It executes its child tasks sequentially, from first to last, until one of them returns [FAILURE], or all of them result in [SUCCESS]. In other words, if any child task results in [FAILURE], the Sequence execution will be aborted, and the Sequence itself will return [FAILURE].

In our example, you can observe a simple [comp]Sequence[/comp] in action. Notice how it fails when it reaches the [act]Fail[/act] action and never proceeds to the [act]Wait 888 sec[/act] action afterward. By the way, the simple [act]Fail[/act] task is quite useful for troubleshooting. Its sole function is to return [FAILURE].
"
blackboard_plan = SubResource("BlackboardPlan_ewfwq")
root_task = SubResource("BTSequence_vx4uy")
