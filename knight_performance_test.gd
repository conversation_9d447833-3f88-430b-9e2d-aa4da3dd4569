extends Node
class_name KnightPerformanceTest

# Performance testing script for optimized knight AI
# Attach this to a Node in your test scene to monitor knight performance

@export var test_duration: float = 60.0  # Test for 60 seconds
@export var target_knight_count: int = 25  # Target number of knights to spawn
@export var spawn_radius: float = 20.0  # Radius around player to spawn knights
@export var knight_scene: PackedScene  # Assign the knight scene in inspector
@export var trigger_doom_at: float = 30.0  # Trigger DOOM mode at this time (seconds)

var test_timer: float = 0.0
var knights: Array[QuakeKnight] = []
var performance_data: Array[Dictionary] = []
var player: Node3D
var doom_triggered: bool = false

func _ready():
	print("=== PERFORMANCE TEST SCRIPT STARTING ===")
	print("[Performance Test] Script is running and _ready() was called")
	print("[Performance Test] Current scene: %s" % get_tree().current_scene.name)

	player = get_tree().get_first_node_in_group("player")
	if not player:
		print("[Performance Test] ERROR: No player found in 'player' group")
		print("[Performance Test] Available groups: %s" % str(get_tree().get_groups()))
		var all_nodes = get_tree().get_nodes_in_group("player")
		print("[Performance Test] Nodes in player group: %d" % all_nodes.size())
		return

	print("[Performance Test] Found player: %s at position %s" % [player.name, str(player.global_position)])

	if not knight_scene:
		print("[Performance Test] ERROR: Knight scene not assigned in inspector")
		print("[Performance Test] Please assign the knight scene to the knight_scene property")
		print("[Performance Test] Expected path should be something like: res://scenes/enemy/quake/knight_full.tscn")
		return

	print("[Performance Test] Knight scene assigned: %s" % knight_scene.resource_path)
	spawn_test_knights()
	print("[Performance Test] Final knight count in array: %d" % knights.size())

	# Wait for deferred calls to complete, then verify
	await get_tree().process_frame
	await get_tree().process_frame  # Wait an extra frame for deferred calls

	var scene_knights = get_tree().get_nodes_in_group("enemy")
	print("[Performance Test] Knights found in 'enemy' group: %d" % scene_knights.size())

	# Additional verification - check if knights are visible and positioned correctly
	for i in range(min(3, knights.size())):  # Check first 3 knights
		var knight = knights[i]
		if is_instance_valid(knight):
			print("[Performance Test] Knight %d: Position=%s, Visible=%s, In Scene Tree=%s" % [
				i, str(knight.global_position), knight.visible, knight.is_inside_tree()
			])
		else:
			print("[Performance Test] Knight %d: INVALID" % i)

func spawn_test_knights():
	print("=== SPAWNING KNIGHTS ===")
	print("[Performance Test] Target knight count: %d" % target_knight_count)
	print("[Performance Test] Player position: %s" % str(player.global_position))
	print("[Performance Test] Spawn radius: %f" % spawn_radius)
	print("[Performance Test] Current scene children before spawn: %d" % get_tree().current_scene.get_child_count())

	if not knight_scene:
		print("[Performance Test] CRITICAL ERROR: knight_scene is null!")
		return

	print("[Performance Test] Knight scene resource path: %s" % knight_scene.resource_path)

	var successful_spawns = 0
	var failed_spawns = 0

	for i in range(target_knight_count):
		print("[Performance Test] Attempting to spawn knight %d..." % i)

		var knight_instance = knight_scene.instantiate()
		if not knight_instance:
			print("[Performance Test] ERROR: instantiate() returned null for knight %d" % i)
			failed_spawns += 1
			continue

		print("[Performance Test] Knight %d instantiated successfully, type: %s" % [i, knight_instance.get_class()])

		# Try to cast to QuakeKnight
		var knight = knight_instance as QuakeKnight
		if not knight:
			print("[Performance Test] ERROR: Knight %d is not a QuakeKnight! Type: %s" % [i, knight_instance.get_class()])
			knight_instance.queue_free()
			failed_spawns += 1
			continue

		# Random position around player
		var angle = randf() * TAU
		var distance = randf_range(5.0, spawn_radius)
		var spawn_pos = player.global_position + Vector3(
			cos(angle) * distance,
			2.0,  # Spawn 2 meters above ground to prevent underground spawning
			sin(angle) * distance
		)

		print("[Performance Test] Setting knight %d position to: %s" % [i, str(spawn_pos)])
		knight.global_position = spawn_pos
		knight.performance_debug = true  # Enable performance monitoring

		print("[Performance Test] Adding knight %d to scene tree..." % i)
		get_tree().current_scene.add_child.call_deferred(knight)
		knights.append(knight)
		successful_spawns += 1

		print("[Performance Test] Knight %d successfully queued for scene addition" % i)

	print("=== SPAWN RESULTS ===")
	print("[Performance Test] Successful spawns: %d" % successful_spawns)
	print("[Performance Test] Failed spawns: %d" % failed_spawns)
	print("[Performance Test] Knights array size: %d" % knights.size())
	print("[Performance Test] Current scene children after spawn: %d" % get_tree().current_scene.get_child_count())

func _process(delta):
	test_timer += delta

	# Trigger DOOM mode at specified time
	if not doom_triggered and test_timer >= trigger_doom_at:
		trigger_doom_mode()
		doom_triggered = true

	# Collect performance data every second
	if int(test_timer) != int(test_timer - delta):
		collect_performance_data(delta)

	# End test after duration
	if test_timer >= test_duration:
		end_performance_test()
		set_process(false)

func collect_performance_data(delta: float):
	var frame_data = {
		"timestamp": test_timer,
		"fps": Engine.get_frames_per_second(),
		"global_stats": QuakeKnight.get_global_performance_stats(),
		"knight_count": knights.size(),
		"active_knights": 0,
		"lod_distribution": {},
		"avg_frame_time": 0.0
	}
	
	var total_frame_time = 0.0
	var active_count = 0
	var doom_count = 0

	var valid_knights = 0
	var invalid_knights = 0

	for knight in knights:
		if not is_instance_valid(knight):
			invalid_knights += 1
			continue

		valid_knights += 1
		var knight_info = knight.get_performance_info()
		var lod_level = knight_info.get("effective_lod_level", 0)

		if not knight_info.get("is_culled", false):
			active_count += 1

		if knight_info.get("is_doom_state", false):
			doom_count += 1

		# Track LOD distribution
		if lod_level in frame_data.lod_distribution:
			frame_data.lod_distribution[lod_level] += 1
		else:
			frame_data.lod_distribution[lod_level] = 1

		total_frame_time += knight_info.get("avg_frame_time_ms", 0.0)

	# Debug info every 10 seconds
	if int(test_timer) % 10 == 0 and int(test_timer) != int(test_timer - delta):
		print("[Performance Test] DEBUG: Valid knights: %d, Invalid knights: %d, Active: %d" % [valid_knights, invalid_knights, active_count])
	
	frame_data.active_knights = active_count
	frame_data.avg_frame_time = total_frame_time / max(1, knights.size())
	frame_data["doom_knights"] = doom_count

	performance_data.append(frame_data)

	# Print real-time stats
	print("[Performance Test] T:%.1fs FPS:%.1f Active:%d/%d DOOM:%d Emergency:%s AvgTime:%.2fms" % [
		test_timer,
		frame_data.fps,
		active_count,
		knights.size(),
		doom_count,
		"YES" if frame_data.global_stats.get("emergency_mode", false) else "NO",
		frame_data.avg_frame_time
	])

func end_performance_test():
	print("\n[Performance Test] Test completed! Generating report...")
	generate_performance_report()
	cleanup_test_knights()

func generate_performance_report():
	if performance_data.is_empty():
		print("[Performance Test] No performance data collected")
		return
	
	var total_fps = 0.0
	var min_fps = 999.0
	var max_fps = 0.0
	var emergency_activations = 0
	var total_frame_time = 0.0
	
	for data in performance_data:
		var fps = data.fps
		total_fps += fps
		min_fps = min(min_fps, fps)
		max_fps = max(max_fps, fps)
		total_frame_time += data.avg_frame_time
		
		if data.global_stats.get("emergency_mode", false):
			emergency_activations += 1
	
	var avg_fps = total_fps / performance_data.size()
	var avg_frame_time = total_frame_time / performance_data.size()
	
	print("\n=== KNIGHT AI PERFORMANCE TEST RESULTS ===")
	print("Test Duration: %.1f seconds" % test_duration)
	print("Target Knights: %d" % target_knight_count)
	print("Average FPS: %.1f" % avg_fps)
	print("Minimum FPS: %.1f" % min_fps)
	print("Maximum FPS: %.1f" % max_fps)
	print("Average Frame Time: %.2f ms" % avg_frame_time)
	print("Emergency Mode Activations: %d/%d samples (%.1f%%)" % [
		emergency_activations, 
		performance_data.size(),
		(emergency_activations * 100.0) / performance_data.size()
	])
	
	# Performance assessment
	var performance_grade = "EXCELLENT"
	if avg_fps < 50:
		performance_grade = "POOR"
	elif avg_fps < 55:
		performance_grade = "FAIR"
	elif avg_fps < 58:
		performance_grade = "GOOD"
	
	print("Performance Grade: %s" % performance_grade)
	
	if avg_fps >= 58 and min_fps >= 45:
		print("✓ PASSED: Consistent 60fps performance achieved with %d knights" % target_knight_count)
	else:
		print("✗ FAILED: Performance target not met")
		print("  Recommendation: Reduce knight count or adjust LOD settings")
	
	print("==========================================\n")

func trigger_doom_mode():
	print("[Performance Test] Triggering DOOM mode for all knights...")
	for knight in knights:
		if is_instance_valid(knight):
			knight.boost_speed(2.0)  # This sets _force_chase = true and doubles speed
	print("[Performance Test] DOOM mode activated - knights are now in relentless pursuit!")

# Manual test function you can call from the debugger or script
func manual_spawn_test():
	print("=== MANUAL SPAWN TEST ===")
	if not knight_scene:
		print("ERROR: No knight scene assigned!")
		return

	var test_knight = knight_scene.instantiate() as QuakeKnight
	if not test_knight:
		print("ERROR: Failed to instantiate knight!")
		return

	test_knight.global_position = Vector3(0, 2, 0)  # Spawn at origin, 2m up
	get_tree().current_scene.add_child.call_deferred(test_knight)
	print("SUCCESS: Test knight queued for spawn at origin")

func cleanup_test_knights():
	print("[Performance Test] Cleaning up %d knights..." % knights.size())
	for knight in knights:
		if is_instance_valid(knight):
			knight.queue_free()
	knights.clear()
	print("[Performance Test] Cleanup complete")
