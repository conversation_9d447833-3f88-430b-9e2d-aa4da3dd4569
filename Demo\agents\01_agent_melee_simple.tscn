[gd_scene load_steps=5 format=3 uid="uid://1dnm7tuik8xo"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_l180o"]
[ext_resource type="Texture2D" uid="uid://cw8s50856x8ct" path="res://demo/assets/agent_melee_simple.png" id="2_bvbes"]
[ext_resource type="BehaviorTree" uid="uid://bpdm5jnegi38" path="res://demo/ai/trees/01_agent_melee_simple.tres" id="3_tb7cx"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_s08ac"]

[node name="AgentMeleeSimple" instance=ExtResource("1_l180o")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_bvbes")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_bvbes")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_bvbes")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_bvbes")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_bvbes")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_bvbes")

[node name="Health" parent="." index="3"]
max_health = 3.0

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_tb7cx")
blackboard_plan = SubResource("BlackboardPlan_s08ac")
