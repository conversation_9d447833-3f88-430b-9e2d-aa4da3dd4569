[gd_scene load_steps=16 format=3 uid="uid://b3ae14mc2ty3y"]

[ext_resource type="Script" uid="uid://qpj1dk7ugnja" path="res://demo/scenes/showcase.gd" id="1_l12ql"]
[ext_resource type="Theme" uid="uid://boqtjf88xcpu4" path="res://demo/assets/ui.theme" id="2_3d7dj"]
[ext_resource type="FontFile" uid="uid://d25vkmce7mhlt" path="res://demo/assets/fonts/knewave_regular.ttf" id="3_7vli5"]
[ext_resource type="FontFile" uid="uid://n231m6uqsq6x" path="res://demo/assets/fonts/junction_bold.otf" id="4_7ubu6"]
[ext_resource type="Texture2D" uid="uid://dc1wu244fnetf" path="res://demo/assets/arrow_left.png" id="5_bvrtp"]
[ext_resource type="Texture2D" uid="uid://tyu0ua1ju38l" path="res://demo/assets/logo.png" id="5_rwygb"]
[ext_resource type="Script" uid="uid://c5p1i8ofpv7qn" path="res://demo/scenes/base/toggle_fullscreen.gd" id="6_uggpo"]
[ext_resource type="Texture2D" uid="uid://bjakugmqbbtw7" path="res://demo/assets/arrow_right.png" id="7_5do2y"]
[ext_resource type="PackedScene" uid="uid://bsig1usigbbuy" path="res://demo/scenes/base/arena.tscn" id="7_42nq6"]
[ext_resource type="PackedScene" uid="uid://c5fhe3tulhlco" path="res://demo/props/dummy.tscn" id="8_apshw"]
[ext_resource type="Script" uid="uid://cb8thyd7vi2nj" path="res://demo/scenes/base/code_edit.gd" id="9_txke7"]

[sub_resource type="LabelSettings" id="LabelSettings_rdr7a"]
font = ExtResource("3_7vli5")
font_size = 33
outline_size = 11
outline_color = Color(0.258915, 0.234974, 0.191974, 1)

[sub_resource type="LabelSettings" id="LabelSettings_3uhve"]
font = ExtResource("3_7vli5")
font_size = 26
font_color = Color(0.804089, 0.811917, 0.823636, 1)
outline_size = 10
outline_color = Color(0.145083, 0.168536, 0.203708, 1)

[sub_resource type="LabelSettings" id="LabelSettings_la4ui"]
font = ExtResource("3_7vli5")
font_size = 36
font_color = Color(1, 1, 0.239216, 1)
outline_size = 20
outline_color = Color(0.211521, 0.23888, 0.290166, 1)

[sub_resource type="LabelSettings" id="LabelSettings_tlprv"]
font = ExtResource("3_7vli5")
font_size = 23
font_color = Color(0.804089, 0.811917, 0.823636, 1)
outline_size = 10
outline_color = Color(0.145083, 0.168536, 0.203708, 1)

[node name="AgentShowcase" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_l12ql")

[node name="UI Layer" type="CanvasLayer" parent="."]

[node name="Control" type="Control" parent="UI Layer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme = ExtResource("2_3d7dj")

[node name="Toolbar" type="PanelContainer" parent="UI Layer/Control"]
custom_minimum_size = Vector2(0, 54.26)
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_right = -612.0
offset_bottom = 58.0
grow_horizontal = 2

[node name="HBoxContainer" type="HBoxContainer" parent="UI Layer/Control/Toolbar"]
layout_mode = 2
theme_override_constants/separation = 4

[node name="ToggleFullscreen" type="Button" parent="UI Layer/Control/Toolbar/HBoxContainer"]
layout_mode = 2
focus_mode = 0
theme_override_fonts/font = ExtResource("3_7vli5")
theme_override_font_sizes/font_size = 22
text = "Toggle Fullscreen"
script = ExtResource("6_uggpo")

[node name="SwitchToGame" type="Button" parent="UI Layer/Control/Toolbar/HBoxContainer"]
layout_mode = 2
focus_mode = 0
theme_override_fonts/font = ExtResource("3_7vli5")
theme_override_font_sizes/font_size = 22
text = "Switch to Game"

[node name="BeginTutorial" type="Button" parent="UI Layer/Control/Toolbar/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
theme_override_fonts/font = ExtResource("3_7vli5")
theme_override_font_sizes/font_size = 22
text = "Begin Tutorial
"

[node name="SceneTitle" type="Label" parent="UI Layer/Control/Toolbar/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 6
text = "Tutorial"
label_settings = SubResource("LabelSettings_rdr7a")

[node name="NavigationHint" type="Label" parent="UI Layer/Control/Toolbar/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
size_flags_horizontal = 8
text = "Select Agent ➤"
label_settings = SubResource("LabelSettings_3uhve")
horizontal_alignment = 2

[node name="BehaviorInspector" type="PanelContainer" parent="UI Layer/Control"]
layout_mode = 1
anchors_preset = 11
anchor_left = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -612.0
grow_horizontal = 0
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="UI Layer/Control/BehaviorInspector"]
custom_minimum_size = Vector2(600, 0)
layout_mode = 2
theme_override_constants/separation = 4

[node name="HBoxContainer" type="HBoxContainer" parent="UI Layer/Control/BehaviorInspector/VBoxContainer"]
layout_mode = 2
theme_override_constants/separation = 4

[node name="Previous" type="Button" parent="UI Layer/Control/BehaviorInspector/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
theme_override_font_sizes/font_size = 28
icon = ExtResource("5_bvrtp")

[node name="AgentSelection" type="MenuButton" parent="UI Layer/Control/BehaviorInspector/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(400, 50)
layout_mode = 2
size_flags_horizontal = 3
theme_override_fonts/font = ExtResource("4_7ubu6")
theme_override_font_sizes/font_size = 30
text = "02_agent_imp.tres
"
flat = false

[node name="Next" type="Button" parent="UI Layer/Control/BehaviorInspector/VBoxContainer/HBoxContainer"]
unique_name_in_owner = true
layout_mode = 2
focus_mode = 0
theme_override_font_sizes/font_size = 28
icon = ExtResource("7_5do2y")

[node name="BehaviorTreeView" type="BehaviorTreeView" parent="UI Layer/Control/BehaviorInspector/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(600, 0)
layout_mode = 2
size_flags_vertical = 3

[node name="PanelContainer" type="PanelContainer" parent="UI Layer/Control"]
layout_mode = 1
anchors_preset = 12
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = -8.0
offset_right = -612.0
grow_horizontal = 2
grow_vertical = 0

[node name="Control" type="Control" parent="UI Layer/Control/PanelContainer"]
layout_mode = 2

[node name="Logo" type="TextureRect" parent="UI Layer/Control/PanelContainer/Control"]
layout_mode = 1
offset_top = -128.0
offset_right = 256.0
offset_bottom = -43.0
texture = ExtResource("5_rwygb")

[node name="Demo project" type="Label" parent="UI Layer/Control/PanelContainer/Control/Logo"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -316.0
offset_top = -72.0
offset_right = 13.0
offset_bottom = 74.0
grow_horizontal = 0
grow_vertical = 0
text = "demo project"
label_settings = SubResource("LabelSettings_la4ui")
horizontal_alignment = 2
vertical_alignment = 1

[node name="Header" type="HBoxContainer" parent="UI Layer/Control/PanelContainer/Control"]
layout_mode = 2
offset_left = 3.0
offset_top = -43.0
offset_right = 1299.0
offset_bottom = -7.0
theme_override_constants/separation = 8

[node name="Label" type="Label" parent="UI Layer/Control/PanelContainer/Control/Header"]
layout_mode = 2
size_flags_horizontal = 10
text = "Description"
label_settings = SubResource("LabelSettings_tlprv")

[node name="MinimizeDescription" type="Button" parent="UI Layer/Control/PanelContainer/Control/Header"]
unique_name_in_owner = true
custom_minimum_size = Vector2(32, 32)
layout_mode = 2
size_flags_horizontal = 4
size_flags_vertical = 4
focus_mode = 0
theme_override_fonts/font = ExtResource("3_7vli5")
text = "-"

[node name="MarginContainer" type="MarginContainer" parent="UI Layer/Control/PanelContainer"]
layout_mode = 2
theme_override_constants/margin_left = 20
theme_override_constants/margin_top = 6
theme_override_constants/margin_bottom = 6

[node name="VBoxContainer" type="VBoxContainer" parent="UI Layer/Control/PanelContainer/MarginContainer"]
layout_mode = 2
theme_override_constants/separation = 0

[node name="Description" type="RichTextLabel" parent="UI Layer/Control/PanelContainer/MarginContainer/VBoxContainer"]
unique_name_in_owner = true
custom_minimum_size = Vector2(0, 140)
layout_mode = 2
size_flags_vertical = 3
theme_override_font_sizes/bold_italics_font_size = 20
theme_override_font_sizes/italics_font_size = 20
theme_override_font_sizes/mono_font_size = 20
theme_override_font_sizes/normal_font_size = 20
theme_override_font_sizes/bold_font_size = 20
bbcode_enabled = true
text = "[b]Behavior Trees[/b] are composed of tasks that represent specific actions or decision-making rules. Tasks can be broadly categorized into two main types: control tasks and leaf tasks. Control tasks determine the execution flow within the tree. They include Sequence, Selector, and Invert. Leaf tasks represent specific actions to perform, like moving or attacking, or conditions that need to be checked. The BTTask class provides the foundation for various building blocks of the Behavior Trees. BT tasks can share data with the help of the Blackboard."
fit_content = true

[node name="CodePopup" type="PopupPanel" parent="UI Layer/Control"]
unique_name_in_owner = true
position = Vector2i(0, 60)
size = Vector2i(1024, 708)
visible = true

[node name="CodeEdit" type="CodeEdit" parent="UI Layer/Control/CodePopup"]
unique_name_in_owner = true
custom_minimum_size = Vector2(800, 700)
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 4.0
offset_top = 4.0
offset_right = -4.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
gutters_draw_line_numbers = true
script = ExtResource("9_txke7")

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(400, 0)
zoom = Vector2(0.88, 0.88)
process_callback = 0
position_smoothing_enabled = true
drag_horizontal_enabled = true
drag_vertical_enabled = true
drag_left_margin = 0.1
drag_top_margin = 0.1
drag_right_margin = 0.0
drag_bottom_margin = 0.1

[node name="Arena" parent="." instance=ExtResource("7_42nq6")]
metadata/_edit_lock_ = true

[node name="Dummy" parent="." instance=ExtResource("8_apshw")]
position = Vector2(1106, 423)

[connection signal="pressed" from="UI Layer/Control/Toolbar/HBoxContainer/SwitchToGame" to="." method="_on_switch_to_game_pressed"]
[connection signal="pressed" from="UI Layer/Control/Toolbar/HBoxContainer/BeginTutorial" to="." method="_on_tutorial_pressed"]
[connection signal="task_selected" from="UI Layer/Control/BehaviorInspector/VBoxContainer/BehaviorTreeView" to="." method="_on_behavior_tree_view_task_selected"]
[connection signal="button_down" from="UI Layer/Control/PanelContainer/Control/Header/MinimizeDescription" to="." method="_on_minimize_description_button_down"]
