[gd_scene load_steps=36 format=3 uid="uid://ooigbfhfy4wa"]

[ext_resource type="Texture2D" uid="uid://dlo1ine6p5di4" path="res://demo/assets/agent_bobby.png" id="1_1u51b"]
[ext_resource type="Script" uid="uid://68wbhtvlbkqm" path="res://demo/agents/scripts/agent_base.gd" id="1_n8vy2"]
[ext_resource type="Texture2D" uid="uid://dj4oayt5ttvh8" path="res://demo/assets/fx.png" id="2_lv102"]
[ext_resource type="Texture2D" uid="uid://dwhhxj5557qrb" path="res://demo/assets/shadow.png" id="2_vr0jo"]
[ext_resource type="Texture2D" uid="uid://2uyxh7sy8qny" path="res://demo/assets/weapon_ninja_star.png" id="4_1c5xq"]
[ext_resource type="Texture2D" uid="uid://bdusy0qqtw0th" path="res://demo/assets/smoke.png" id="4_mnb6v"]
[ext_resource type="Script" uid="uid://5byn62y1oa76" path="res://demo/agents/scripts/health.gd" id="4_qwtrf"]
[ext_resource type="Script" uid="uid://dntc3c582m5w8" path="res://demo/agents/scripts/hitbox.gd" id="5_taq6b"]
[ext_resource type="Script" uid="uid://dq5h6yydckjc1" path="res://demo/agents/scripts/hurtbox.gd" id="6_jnvxm"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2k81i"]
size = Vector2(140, 40)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_26abe"]
size = Vector2(88, 40)

[sub_resource type="Curve" id="Curve_noh01"]
_data = [Vector2(0, 0.010989), 0.0, 0.0, 0, 0, Vector2(0.151111, 1), 1.3559, 1.3559, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_sb0wp"]
curve = SubResource("Curve_noh01")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_f5i1x"]
lifetime_randomness = 0.5
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(40, 1, 1)
direction = Vector3(0, -1, 0)
spread = 90.0
initial_velocity_min = 150.0
initial_velocity_max = 250.0
angular_velocity_min = -90.0
angular_velocity_max = 90.0
gravity = Vector3(0, 0, 0)
radial_accel_min = -90.0
radial_accel_max = 90.0
damping_min = 200.0
damping_max = 200.0
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_sb0wp")

[sub_resource type="Curve" id="Curve_e33mb"]
_data = [Vector2(0, 0.010989), 0.0, 0.0, 0, 0, Vector2(0.151111, 1), 1.3559, 1.3559, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_qti8k"]
curve = SubResource("Curve_e33mb")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_gmal1"]
lifetime_randomness = 0.5
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(40, 1, 1)
direction = Vector3(0, -1, 0)
spread = 90.0
initial_velocity_min = 150.0
initial_velocity_max = 250.0
angular_velocity_min = -90.0
angular_velocity_max = 90.0
gravity = Vector3(0, 0, 0)
radial_accel_min = -90.0
radial_accel_max = 90.0
damping_min = 200.0
damping_max = 200.0
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_qti8k")
color = Color(0.913725, 0.8, 0.898039, 1)

[sub_resource type="CircleShape2D" id="CircleShape2D_0c228"]

[sub_resource type="Animation" id="Animation_nvm4d"]
resource_name = "RESET"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/LegR:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(31, 65)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/LegR:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/LegR:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/Body:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(5, 53)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/Body:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/Body:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body/Hat:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/HandL:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-60, -53)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/HandR:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(51, -51)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/LegL:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-29, 65)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/LegL:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/LegL:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("FX/Cloud:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig:modulate")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Rig:position")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -76)]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Root/Rig:rotation")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/23/type = "value"
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/path = NodePath("Root/Rig:scale")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/24/type = "value"
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/path = NodePath("FX/Death:emitting")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/25/type = "value"
tracks/25/imported = false
tracks/25/enabled = true
tracks/25/path = NodePath("Root/Rig/Body/HandL/WeaponNinjaStar:visible")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/26/type = "value"
tracks/26/imported = false
tracks/26/enabled = true
tracks/26/path = NodePath("Root/Hitbox:knockback_enabled")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/27/type = "value"
tracks/27/imported = false
tracks/27/enabled = true
tracks/27/path = NodePath("Root/Hurtbox/HurtboxCollisionShape2D:disabled")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_8wj70"]
resource_name = "attack_1"
length = 0.28
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(0, -76), Vector2(0, -76)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegL:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-48, 48), Vector2(-55, 35), Vector2(-55, 35), Vector2(-29, 65)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegL:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.918872, 1.36873, 1.36873, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegL:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/LegR:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(35, 68), Vector2(31, 65)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/LegR:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [-0.125403, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/LegR:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53), Vector2(5, 53)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.204683, 0.274184, 0.274184, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1.1, 0.9), Vector2(1.1, 0.9), Vector2(1.1, 0.9), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/Hat:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(2, -91), Vector2(2, -91)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandL:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-77.4704, -36.9794), Vector2(71.4321, -75.3422), Vector2(71.4321, -75.3422), Vector2(-60, -53)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.8, 1.8), Vector2(1.8, 1.8), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/HandR:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(71.4504, -76.2001), Vector2(-47.4494, -37.9368), Vector2(-47.4494, -37.9368), Vector2(51, -51)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1.6, 1.6), Vector2(1.6, 1.6), Vector2(1.6, 1.6), Vector2(1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.1, 0.15, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [true, false, true, true]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Root/Hitbox:knockback_enabled")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [false, false, false]
}

[sub_resource type="Animation" id="Animation_s1dey"]
resource_name = "attack_2"
length = 0.3
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -76)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegL:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-29, 65), Vector2(-48, 48)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegL:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.397498, 0.918872]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegL:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/LegR:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(50, 51), Vector2(35, 68)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/LegR:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.401688, -0.125403]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/LegR:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.278326, 0.204683]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.9, 1.1), Vector2(1.1, 0.9)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/Hat:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandL:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-1.7771, -44.1117), Vector2(-77.4704, -36.9794)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/HandR:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(-70.2132, -64.2738), Vector2(71.4504, -76.2001)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.15, 0.25),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.6, 1.6)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.25, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Root/Hitbox:knockback_enabled")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [false, false, false]
}

[sub_resource type="Animation" id="Animation_g7a0r"]
resource_name = "attack_3"
length = 0.35
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/Body:position")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(-5, 40), Vector2(-5, 40)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/Body:rotation")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.349066, 0.436332]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/Body:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.9, 1.1), Vector2(1.1, 0.9)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/Body/Hat:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body/HandL:position")
tracks/6/interp = 2
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.2, 0.25, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-93.4894, -42.3521), Vector2(6.40698, 1.6281), Vector2(68.5276, -75.9627)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/7/interp = 2
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.2, 0.25, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 1.70101, 0.0, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(2, 2), Vector2(3, 3)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/HandR:position")
tracks/9/interp = 2
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(-1.40495, -52.6598), Vector2(-66.8689, -27.8787)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/10/interp = 2
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/LegL:position")
tracks/12/interp = 2
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-57, 59), Vector2(37, 63)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/LegL:rotation")
tracks/13/interp = 2
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.706805, -0.742333]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/LegL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/LegR:position")
tracks/15/interp = 2
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(31, 65), Vector2(-45, 39)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/LegR:rotation")
tracks/16/interp = 2
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.477383, 1.06217]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/LegR:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("FX/Cloud:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.25, 0.35),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, false, true]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Hitbox:knockback_enabled")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0.2, 0.35),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}

[sub_resource type="Animation" id="Animation_jcwlt"]
resource_name = "charge"
length = 0.2
loop_mode = 1
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -76)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegL:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(-16, 67), Vector2(-43, 30), Vector2(17, 26), Vector2(51, 50), Vector2(-16, 67)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegL:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [0.518326, 0.991314, 0.518326, -1.96385, 0.518326]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegL:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/LegR:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(23, 69), Vector2(74, 43), Vector2(6, 23), Vector2(-35, 47), Vector2(23, 69)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/LegR:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [0.811128, -0.351373, 0.811128, 1.40327, 0.811128]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/LegR:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53), Vector2(5, 53), Vector2(5, 53)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [0.553472, 0.61588, 0.553472, 0.61588, 0.553472]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/Hat:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1.1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandL:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(8.86381, -27.008), Vector2(17.3173, -25.7316), Vector2(8.86381, -27.008), Vector2(17.3173, -25.7316), Vector2(8.86381, -27.008)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/HandR:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(22.4758, -47.2741)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, false]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Root/Hitbox:knockback_enabled")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, true]
}

[sub_resource type="Animation" id="Animation_sva81"]
resource_name = "charge_prepare"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -76)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegL:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-46, 66), Vector2(-16, 67)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegL:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.518326, 0.518326]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegL:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/LegR:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(55, 35), Vector2(23, 69)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/LegR:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -1.03322, 0.811128]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/LegR:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.455647, 0.553472]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.1, 0.9), Vector2(0.9, 1.1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/Hat:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(2, -91), Vector2(2, -91), Vector2(2, -91)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0872665]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandL:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-77.6176, -56.3868), Vector2(36.6639, -43.1139)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/HandR:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(-42.71, -65.1758), Vector2(48.1987, -60.7003)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_5k1rd"]
resource_name = "dance"
length = 1.8
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/Body/Hat:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 1.8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(2, -91), Vector2(2, -91), Vector2(2, -91)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1, 1.8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1, 1.8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/Body/HandR:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(47.3684, -50.4762), Vector2(36, -51), Vector2(66, -51), Vector2(36, -51), Vector2(66, -51), Vector2(66, -21), Vector2(66, -81), Vector2(66, -21), Vector2(66, -81), Vector2(66, -21), Vector2(47.3684, -50.4762)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.1, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53), Vector2(5, 56), Vector2(5, 53), Vector2(5, 56), Vector2(5, 53), Vector2(5, 56), Vector2(5, 53)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.1, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 1.05), Vector2(0.95, 1.05), Vector2(1.05, 0.95), Vector2(0.95, 1.05), Vector2(1.05, 0.95), Vector2(0.95, 1.05), Vector2(1.05, 0.95), Vector2(0.95, 1.05), Vector2(1.05, 0.95), Vector2(0.95, 1.05), Vector2(0.95, 1.05)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/LegR:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(46, 65), Vector2(16, 65), Vector2(46, 65), Vector2(16, 65), Vector2(31, 65), Vector2(31, 65), Vector2(31, 65)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/LegR:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/LegR:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/LegL:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-14, 65), Vector2(-44, 65), Vector2(-14, 65), Vector2(-44, 65), Vector2(-29, 65), Vector2(-29, 65), Vector2(-29, 65)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/LegL:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/LegL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.1, 1.8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0, -76), Vector2(0, -76), Vector2(0, -76)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.1, 1.8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.1, 1.8),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/HandL:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(-31.5789, -50.4762), Vector2(-75, -53), Vector2(-15, -53), Vector2(-75, -53), Vector2(-15, -53), Vector2(-15, -83), Vector2(-15, -23), Vector2(-15, -83), Vector2(-15, -23), Vector2(-15, -83), Vector2(-31.5789, -50.4762)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.8),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_wei72"]
resource_name = "death"
length = 0.5
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/LegL:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-29, 65), Vector2(-29, 65)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/LegL:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, -1.0566]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/LegL:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegR:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(31, 65), Vector2(-12.2578, 72.9572)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegR:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, -0.693514]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegR:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(5, 53)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/Hat:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/HandL:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-60, -53), Vector2(-34.0705, -33.1026)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandR:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(51, -51), Vector2(27.9362, -34.8835)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.15, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = false
tracks/18/path = NodePath("FX/Cloud:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig:position")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0, -76), Vector2(-5, -120), Vector2(0, -40)]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig:rotation")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.801138, -1.60228]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Rig:scale")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Root/Rig:modulate")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3, 0.4, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 1), Color(1, 1, 1, 0.321569), Color(1, 1, 1, 0.588235), Color(1, 1, 1, 0)]
}
tracks/23/type = "value"
tracks/23/imported = false
tracks/23/enabled = true
tracks/23/path = NodePath("FX/Death:emitting")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = {
"times": PackedFloat32Array(0, 0.3, 0.5),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [false, true, false]
}
tracks/24/type = "value"
tracks/24/imported = false
tracks/24/enabled = true
tracks/24/path = NodePath("Root/Rig/Body/HandL/WeaponNinjaStar:visible")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = {
"times": PackedFloat32Array(0, 0.5),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, false]
}

[sub_resource type="Animation" id="Animation_msfb2"]
resource_name = "dodge"
length = 0.4
capture_included = true
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0, -76), Vector2(0, -38), Vector2(0, -76)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig:rotation")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 2,
"values": [0.0, 2.35619, 6.28319, 12.5664]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 0.5), Vector2(1, 0.5), Vector2(1, 0.5), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegL:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-24.8287, 40.1066), Vector2(-24.8287, 40.1066), Vector2(-29, 65)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegL:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegL:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/LegR:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(26.1794, 41.8884), Vector2(26.1794, 41.8884), Vector2(31, 65)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/LegR:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/LegR:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53), Vector2(5, 53)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/Hat:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(2, -91), Vector2(2, -91), Vector2(2, -91), Vector2(2, -91)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandL:position")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-19.9999, -52.9358), Vector2(-19.9999, -52.9358), Vector2(-60, -53)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/HandR:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(21.9955, -54.1155), Vector2(21.9955, -54.1155), Vector2(51, -51)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0, 0.0]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.1, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.0001, 0.1, 0.2, 0.3, 0.4),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [true, true, true, true, true, true]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Root/Hurtbox/HurtboxCollisionShape2D:disabled")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, false]
}

[sub_resource type="Animation" id="Animation_gowr5"]
resource_name = "hurt"
length = 0.25
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/Body:position")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(-9, 29)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/Body:rotation")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 2),
"update": 0,
"values": [0.0, -0.459832]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/Body:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.9, 1.1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = false
tracks/3/path = NodePath("Root/Rig/Body/Hat:position")
tracks/3/interp = 2
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(2, -91), Vector2(-23.375, -110.918)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/4/interp = 2
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, -0.169267]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body/HandL:position")
tracks/6/interp = 2
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-86.123, -33.8822)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/7/interp = 2
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/HandR:position")
tracks/9/interp = 2
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(-4.62756, -33.3235)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/10/interp = 2
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/LegL:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(-29, 65)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/LegL:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/LegL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/LegR:position")
tracks/15/interp = 2
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(37, 14)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/LegR:rotation")
tracks/16/interp = 2
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, -0.896359]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/LegR:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("FX/Cloud:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.25),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, true]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig:modulate")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.11, 0.22),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(0.721569, 0, 0.0352941, 1), Color(1, 1, 1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Rig/Body/HandL/WeaponNinjaStar:visible")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.25),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, false]
}
tracks/22/type = "value"
tracks/22/imported = false
tracks/22/enabled = true
tracks/22/path = NodePath("Root/Hitbox:knockback_enabled")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = {
"times": PackedFloat32Array(0, 0.25),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, false]
}

[sub_resource type="Animation" id="Animation_gnqgt"]
resource_name = "idle"
length = 0.4
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/LegR:position")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(31, 65), Vector2(31, 65)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/LegR:rotation")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/LegR:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.9, 1.1), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/Body:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 49), Vector2(5, 53)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/Body:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/Body:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.95, 1.05), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body/Hat:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/HandL:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-62, -55), Vector2(-60, -53)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/10/interp = 2
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/HandR:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(49, -53), Vector2(51, -51)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/13/interp = 2
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/LegL:position")
tracks/15/interp = 2
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-29, 65), Vector2(-29, 65)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/LegL:rotation")
tracks/16/interp = 2
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/LegL:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.9, 1.1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("FX/Cloud:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.4),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, true]
}

[sub_resource type="Animation" id="Animation_uow76"]
resource_name = "spit"
length = 0.3
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/LegL:position")
tracks/0/interp = 2
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-57, 44)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/LegL:rotation")
tracks/1/interp = 2
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.730707]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/LegL:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegR:position")
tracks/3/interp = 2
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(46, 48), Vector2(31, 65)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegR:rotation")
tracks/4/interp = 2
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.718681, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegR:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body:position")
tracks/6/interp = 2
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(4, 46), Vector2(4, 46)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body:rotation")
tracks/7/interp = 2
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.330061, 0.363712]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.8, 1.2), Vector2(1.1, 0.9)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/Hat:position")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/HandL:position")
tracks/12/interp = 2
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-53.8926, -21.1176), Vector2(-19.8796, -43.7927)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/13/interp = 2
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandR:position")
tracks/15/interp = 2
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(47.6296, -15.5481), Vector2(47.0064, -56.4127)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/16/interp = 2
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_5mxvi"]
resource_name = "summon"
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/LegL:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(-29, 65)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/LegL:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/LegL:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegR:position")
tracks/3/interp = 2
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(47, 54), Vector2(43, 56.75), Vector2(47, 54), Vector2(43, 56.75), Vector2(31, 65)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegR:rotation")
tracks/4/interp = 2
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, -0.40126, -0.768424, -0.40126, -0.768424, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegR:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body:position")
tracks/6/interp = 2
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.2, 0.6, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53), Vector2(5, 53)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body:rotation")
tracks/7/interp = 2
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.2, 0.6, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 0,
"values": [0.0, -0.179614, -0.179614, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(0.9, 1.1), Vector2(1, 1), Vector2(0.9, 1.1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/Hat:position")
tracks/9/interp = 2
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(2, -91), Vector2(2, -91)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/10/interp = 2
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/HandL:position")
tracks/12/interp = 2
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(-71.1271, -84.0591), Vector2(-76.1533, -94.4691), Vector2(-71.1271, -84.0591), Vector2(-76.1533, -94.4691), Vector2(-60, -53)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/13/interp = 2
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, -0.836345, 0.0, -0.836345, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/Body/HandR:position")
tracks/15/interp = 2
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(61.8744, -83.286), Vector2(62.8802, -95.0655), Vector2(61.8744, -83.286), Vector2(62.8802, -95.0655), Vector2(51, -51)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/16/interp = 2
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [0.0, 0.0, -0.887813, 0.0, -0.887813, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.2, 0.4, 0.6, 0.8, 1),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}

[sub_resource type="Animation" id="Animation_yn0t6"]
resource_name = "throw"
length = 0.1
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -76)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/LegL:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(-48, 64), Vector2(45, 67)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegL:rotation")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.862915, -0.786391]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegR:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(-27, 64)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegR:rotation")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.899266]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [-0.250268, 0.250549]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body/Hat:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/HandR:position")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(37.6892, -51.6964), Vector2(-36.284, -25.5073)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig:scale")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/LegL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/LegR:scale")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/Body:scale")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(0.9, 1.1), Vector2(1.1, 0.9)]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/HandL:position")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-70.3036, -52.0327), Vector2(-5.01252, -88.9993), Vector2(66.2323, -78.0635)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.05, 0.1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1.5, 1.5)]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Rig/Body/HandL/WeaponNinjaStar:visible")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.09, 0.1),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [true, true, false]
}

[sub_resource type="Animation" id="Animation_kb56n"]
resource_name = "throw_prepare"
length = 0.2
loop_mode = 1
step = 0.05
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -76)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/LegL:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-48, 64), Vector2(-48, 64), Vector2(-48, 64)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/LegL:rotation")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.644548, 0.862915, 0.644548]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/LegR:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(31, 65)]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/LegR:rotation")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 53), Vector2(5, 53)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [-0.194275, -0.250268, -0.194275]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body/Hat:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(2, -91)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/HandL:position")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-27.2463, -58.3579), Vector2(-70.3036, -52.0327), Vector2(-27.2463, -58.3579)]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/Body/HandR:position")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(37.6892, -51.6964)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/LegL:scale")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/LegR:scale")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/Body:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1, 1)]
}
tracks/21/type = "value"
tracks/21/imported = false
tracks/21/enabled = true
tracks/21/path = NodePath("Root/Rig/Body/HandL/WeaponNinjaStar:visible")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = {
"times": PackedFloat32Array(0, 0.2),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, true]
}

[sub_resource type="Animation" id="Animation_fh06e"]
resource_name = "walk"
length = 0.6
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Root/Rig/Body:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(5, 53), Vector2(5, 47), Vector2(5, 53)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Root/Rig/Body:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Root/Rig/Body:scale")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(0.92, 1.08), Vector2(1, 1)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("Root/Rig/Body/Hat:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0, 0.6),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(2, -91), Vector2(2, -91)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("Root/Rig/Body/Hat:rotation")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0, 0.6),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [0.0, 0.0]
}
tracks/5/type = "value"
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/path = NodePath("Root/Rig/Body/Hat:scale")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = {
"times": PackedFloat32Array(0, 0.6),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1)]
}
tracks/6/type = "value"
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/path = NodePath("Root/Rig/Body/HandL:position")
tracks/6/interp = 2
tracks/6/loop_wrap = true
tracks/6/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-60, -53), Vector2(17, -22), Vector2(-60, -53)]
}
tracks/7/type = "value"
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/path = NodePath("Root/Rig/Body/HandL:rotation")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/8/type = "value"
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/path = NodePath("Root/Rig/Body/HandL:scale")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/9/type = "value"
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/path = NodePath("Root/Rig/Body/HandR:position")
tracks/9/interp = 2
tracks/9/loop_wrap = true
tracks/9/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(51, -51), Vector2(-26, -29), Vector2(51, -51)]
}
tracks/10/type = "value"
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/path = NodePath("Root/Rig/Body/HandR:rotation")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 0.0, 0.0]
}
tracks/11/type = "value"
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/path = NodePath("Root/Rig/Body/HandR:scale")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1, 1), Vector2(1, 1)]
}
tracks/12/type = "value"
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/path = NodePath("Root/Rig/LegL:position")
tracks/12/interp = 2
tracks/12/loop_wrap = true
tracks/12/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(-29, 65), Vector2(33, 65), Vector2(-29, 65)]
}
tracks/13/type = "value"
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/path = NodePath("Root/Rig/LegL:rotation")
tracks/13/interp = 2
tracks/13/loop_wrap = true
tracks/13/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, -0.991068, 0.0]
}
tracks/14/type = "value"
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/path = NodePath("Root/Rig/LegL:scale")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1.1, 1.1), Vector2(1, 1), Vector2(1.1, 1.1)]
}
tracks/15/type = "value"
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/path = NodePath("Root/Rig/LegR:position")
tracks/15/interp = 2
tracks/15/loop_wrap = true
tracks/15/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(31, 65), Vector2(-30, 65), Vector2(31, 65)]
}
tracks/16/type = "value"
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/path = NodePath("Root/Rig/LegR:rotation")
tracks/16/interp = 2
tracks/16/loop_wrap = true
tracks/16/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [0.0, 1.13756, 0.0]
}
tracks/17/type = "value"
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/path = NodePath("Root/Rig/LegR:scale")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = {
"times": PackedFloat32Array(0, 0.3, 0.6),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.1, 1.1), Vector2(1, 1)]
}
tracks/18/type = "value"
tracks/18/imported = false
tracks/18/enabled = true
tracks/18/path = NodePath("FX/Cloud:visible")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}
tracks/19/type = "value"
tracks/19/imported = false
tracks/19/enabled = true
tracks/19/path = NodePath("Root/Hitbox/HitboxCollisionShape2D:disabled")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = {
"times": PackedFloat32Array(0, 0.6),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [true, true]
}
tracks/20/type = "value"
tracks/20/imported = false
tracks/20/enabled = true
tracks/20/path = NodePath("Root/Hurtbox/HurtboxCollisionShape2D:disabled")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = {
"times": PackedFloat32Array(0, 0.6),
"transitions": PackedFloat32Array(1, 1),
"update": 1,
"values": [false, false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_ws2ti"]
_data = {
&"RESET": SubResource("Animation_nvm4d"),
&"attack_1": SubResource("Animation_8wj70"),
&"attack_2": SubResource("Animation_s1dey"),
&"attack_3": SubResource("Animation_g7a0r"),
&"charge": SubResource("Animation_jcwlt"),
&"charge_prepare": SubResource("Animation_sva81"),
&"dance": SubResource("Animation_5k1rd"),
&"death": SubResource("Animation_wei72"),
&"dodge": SubResource("Animation_msfb2"),
&"hurt": SubResource("Animation_gowr5"),
&"idle": SubResource("Animation_gnqgt"),
&"spit": SubResource("Animation_uow76"),
&"summon": SubResource("Animation_5mxvi"),
&"throw": SubResource("Animation_yn0t6"),
&"throw_prepare": SubResource("Animation_kb56n"),
&"walk": SubResource("Animation_fh06e")
}

[node name="AgentBase" type="CharacterBody2D"]
collision_layer = 2
collision_mask = 3
motion_mode = 1
wall_min_slide_angle = 0.0174533
safe_margin = 0.001
script = ExtResource("1_n8vy2")

[node name="Root" type="Node2D" parent="."]
metadata/_edit_lock_ = true

[node name="Rig" type="Node2D" parent="Root"]
position = Vector2(0, -76)

[node name="Shadow" type="Sprite2D" parent="Root/Rig"]
modulate = Color(1, 1, 1, 0.258824)
position = Vector2(0, 76)
texture = ExtResource("2_vr0jo")

[node name="LegL" type="Sprite2D" parent="Root/Rig"]
position = Vector2(-29, 65)
texture = ExtResource("1_1u51b")
region_enabled = true
region_rect = Rect2(104, 67, 35, 28)

[node name="LegR" type="Sprite2D" parent="Root/Rig"]
position = Vector2(31, 65)
texture = ExtResource("1_1u51b")
region_enabled = true
region_rect = Rect2(105, 97, 34, 27)

[node name="Body" type="Sprite2D" parent="Root/Rig"]
position = Vector2(5, 53)
texture = ExtResource("1_1u51b")
offset = Vector2(-5, -53)
region_enabled = true
region_rect = Rect2(4.03712, 4.21556, 94.9839, 121.271)

[node name="Hat" type="Sprite2D" parent="Root/Rig/Body"]
position = Vector2(2, -91)
texture = ExtResource("1_1u51b")
offset = Vector2(-1, -20)
region_enabled = true
region_rect = Rect2(163, 20, 170, 102)

[node name="HandL" type="Sprite2D" parent="Root/Rig/Body"]
position = Vector2(-60, -53)
texture = ExtResource("1_1u51b")
region_enabled = true
region_rect = Rect2(111.022, 6.27016, 27.6329, 27.1423)

[node name="WeaponNinjaStar" type="Sprite2D" parent="Root/Rig/Body/HandL"]
visible = false
texture = ExtResource("4_1c5xq")

[node name="HandR" type="Sprite2D" parent="Root/Rig/Body"]
show_behind_parent = true
position = Vector2(51, -51)
texture = ExtResource("1_1u51b")
region_enabled = true
region_rect = Rect2(111, 34, 26.9272, 26.6298)

[node name="Hitbox" type="Area2D" parent="Root"]
position = Vector2(50, 0)
collision_layer = 0
collision_mask = 4
script = ExtResource("5_taq6b")
knockback_strength = 1000.0

[node name="HitboxCollisionShape2D" type="CollisionShape2D" parent="Root/Hitbox"]
shape = SubResource("RectangleShape2D_2k81i")
disabled = true
debug_color = Color(0.933131, 0.0801983, 0.605982, 0.42)
metadata/_edit_lock_ = true

[node name="Hurtbox" type="Area2D" parent="Root" node_paths=PackedStringArray("health")]
collision_layer = 8
collision_mask = 0
script = ExtResource("6_jnvxm")
health = NodePath("../../Health")

[node name="HurtboxCollisionShape2D" type="CollisionShape2D" parent="Root/Hurtbox"]
shape = SubResource("RectangleShape2D_26abe")
debug_color = Color(0.466325, 0.590206, 0.107862, 0.42)
metadata/_edit_lock_ = true

[node name="FX" type="Node2D" parent="."]
position = Vector2(0, -68)
metadata/_edit_lock_ = true

[node name="Cloud" type="Sprite2D" parent="FX"]
visible = false
scale = Vector2(0.5, 0.5)
texture = ExtResource("2_lv102")
region_enabled = true
region_rect = Rect2(300, 0, 100, 70)

[node name="Death" type="GPUParticles2D" parent="FX"]
position = Vector2(0, 52)
emitting = false
amount = 16
texture = ExtResource("4_mnb6v")
lifetime = 0.7
one_shot = true
explosiveness = 0.7
fixed_fps = 60
process_material = SubResource("ParticleProcessMaterial_f5i1x")

[node name="Summoned" type="GPUParticles2D" parent="FX"]
position = Vector2(0, 83)
emitting = false
amount = 16
texture = ExtResource("4_mnb6v")
lifetime = 1.4
one_shot = true
explosiveness = 0.5
fixed_fps = 60
process_material = SubResource("ParticleProcessMaterial_gmal1")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2(-1, -7)
rotation = 1.5708
shape = SubResource("CircleShape2D_0c228")

[node name="Health" type="Node" parent="."]
script = ExtResource("4_qwtrf")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
callback_mode_method = 1
libraries = {
&"": SubResource("AnimationLibrary_ws2ti")
}
autoplay = "idle"
