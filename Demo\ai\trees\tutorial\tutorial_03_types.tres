[gd_resource type="BehaviorTree" load_steps=24 format=3 uid="uid://cb0ybf24ahnc3"]

[ext_resource type="Script" uid="uid://bicxffqmm7ek" path="res://demo/ai/tasks/select_random_nearby_pos.gd" id="1_as4cg"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="2_dlpnp"]
[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="3_uv18s"]
[ext_resource type="Script" uid="uid://b7v2utjmtge0x" path="res://demo/ai/tasks/in_range.gd" id="4_k3g8t"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="5_thvy5"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_ewfwq"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_20ku0"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_57u80"]
animation_player = SubResource("BBNode_20ku0")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_11xvc"]
script = ExtResource("1_as4cg")
range_min = 300.0
range_max = 500.0
position_var = &"pos"

[sub_resource type="BTAction" id="BTAction_tdkpj"]
script = ExtResource("2_dlpnp")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTSequence" id="BTSequence_echit"]
children = [SubResource("BTPlayAnimation_57u80"), SubResource("BTAction_11xvc"), SubResource("BTAction_tdkpj")]

[sub_resource type="BTProbability" id="BTProbability_g0ol3"]
children = [SubResource("BTSequence_echit")]

[sub_resource type="BTAction" id="BTAction_124bm"]
script = ExtResource("3_uv18s")
group = &"player"
output_var = &"target"

[sub_resource type="BTCondition" id="BTCondition_n25o8"]
script = ExtResource("4_k3g8t")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BTAction" id="BTAction_1hfgr"]
script = ExtResource("5_thvy5")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_ilr2h"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_mrsu3"]
animation_player = SubResource("BBNode_ilr2h")
animation_name = &"attack_1"

[sub_resource type="BTSequence" id="BTSequence_5vug3"]
children = [SubResource("BTAction_124bm"), SubResource("BTCondition_n25o8"), SubResource("BTAction_1hfgr"), SubResource("BTPlayAnimation_mrsu3")]

[sub_resource type="BBNode" id="BBNode_fq0jf"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_rsejo"]
animation_player = SubResource("BBNode_fq0jf")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_dl31p"]
duration = 2.0

[sub_resource type="BTSequence" id="BTSequence_vx4uy"]
children = [SubResource("BTPlayAnimation_rsejo"), SubResource("BTWait_dl31p")]

[sub_resource type="BTSelector" id="BTSelector_tdpek"]
children = [SubResource("BTProbability_g0ol3"), SubResource("BTSequence_5vug3"), SubResource("BTSequence_vx4uy")]

[resource]
description = "There are four types of tasks:
[ul][act]Actions[/act] are leaf tasks that perform the actual work.[/ul]
[ul][con]Conditions[/con] are leaf tasks that conduct various checks.[/ul]
[ul][comp]Composites[/comp] can have one or more child tasks, and dictate the execution flow of their children.[/ul]
[ul][dec]Decorators[/dec] can only have a single child and they change how their child task operates.[/ul]

Let's take a look into actions first. [act]Actions[/act] are responsible for most of the work and often change the game state in a significant manner. For instance, [act]PlayAnimation[/act] instructs the AnimationPlayer to start a specified animation, with the option to wait for its completion. On the other hand, the [act]Wait[/act] task simply pauses for a set duration before returning [SUCCESS].

Next, we'll explore the core composite tasks, such as [comp]Sequence[/comp] and [comp]Selector[/comp]. These are among the most crucial composites that you should know about."
blackboard_plan = SubResource("BlackboardPlan_ewfwq")
root_task = SubResource("BTSelector_tdpek")
