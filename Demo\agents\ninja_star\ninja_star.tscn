[gd_scene load_steps=10 format=3 uid="uid://bj5n72nomeaci"]

[ext_resource type="Script" uid="uid://ea3wq7unbfaw" path="res://demo/agents/ninja_star/ninja_star.gd" id="1_pja57"]
[ext_resource type="Texture2D" uid="uid://2uyxh7sy8qny" path="res://demo/assets/weapon_ninja_star.png" id="1_ptof7"]
[ext_resource type="Texture2D" uid="uid://dwhhxj5557qrb" path="res://demo/assets/shadow.png" id="2_2rj5a"]
[ext_resource type="Script" uid="uid://dntc3c582m5w8" path="res://demo/agents/scripts/hitbox.gd" id="2_iem02"]
[ext_resource type="Texture2D" uid="uid://bdusy0qqtw0th" path="res://demo/assets/smoke.png" id="5_t37aw"]

[sub_resource type="CircleShape2D" id="CircleShape2D_7uc1b"]
radius = 28.0

[sub_resource type="Curve" id="Curve_ugmtv"]
_data = [Vector2(0, 0.010989), 0.0, 0.0, 0, 0, Vector2(0.151111, 1), 1.3559, 1.3559, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_1f5jp"]
curve = SubResource("Curve_ugmtv")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_gilhy"]
lifetime_randomness = 0.5
particle_flag_disable_z = true
emission_shape = 3
emission_box_extents = Vector3(40, 1, 1)
direction = Vector3(0, -1, 0)
spread = 90.0
initial_velocity_min = 150.0
initial_velocity_max = 250.0
angular_velocity_min = -90.0
angular_velocity_max = 90.0
gravity = Vector3(0, 0, 0)
radial_accel_min = -90.0
radial_accel_max = 90.0
damping_min = 200.0
damping_max = 200.0
scale_min = 0.8
scale_max = 1.2
scale_curve = SubResource("CurveTexture_1f5jp")

[node name="NinjaStar" type="Node2D"]
script = ExtResource("1_pja57")

[node name="Root" type="Node2D" parent="."]

[node name="Shadow" type="Sprite2D" parent="Root"]
modulate = Color(1, 1, 1, 0.686275)
position = Vector2(0, -1)
scale = Vector2(0.382, 0.297)
texture = ExtResource("2_2rj5a")

[node name="NinjaStar" type="Sprite2D" parent="Root"]
position = Vector2(0, -93)
texture = ExtResource("1_ptof7")

[node name="RemoteTransform2D" type="RemoteTransform2D" parent="Root/NinjaStar"]
remote_path = NodePath("../../../Death")
update_rotation = false
update_scale = false

[node name="Hitbox" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 4
script = ExtResource("2_iem02")

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
shape = SubResource("CircleShape2D_7uc1b")
debug_color = Color(0.839216, 0.192157, 0.815686, 0.419608)

[node name="Death" type="GPUParticles2D" parent="."]
position = Vector2(0, -93)
emitting = false
amount = 1
process_material = SubResource("ParticleProcessMaterial_gilhy")
texture = ExtResource("5_t37aw")
lifetime = 0.7
one_shot = true
explosiveness = 0.7
fixed_fps = 60
local_coords = true

[connection signal="area_entered" from="Hitbox" to="." method="_on_hitbox_area_entered"]
