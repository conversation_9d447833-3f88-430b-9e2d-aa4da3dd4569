[gd_scene load_steps=5 format=3 uid="uid://brq6c843j1eeo"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_k4qfc"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_q4r1n"]
[ext_resource type="BehaviorTree" uid="uid://tep50j4d6kgp" path="res://demo/ai/trees/tutorial/tutorial_07_more_decorators.tres" id="3_ta3g6"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialMoreDecorators" instance=ExtResource("1_k4qfc")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_q4r1n")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_q4r1n")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_q4r1n")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_q4r1n")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_q4r1n")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_q4r1n")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_ta3g6")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
