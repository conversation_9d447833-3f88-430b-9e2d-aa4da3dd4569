[gd_scene load_steps=7 format=3 uid="uid://nh4clbb0scls"]

[ext_resource type="Script" uid="uid://duu4rdasluvcw" path="res://knight_performance_test.gd" id="1_f4dpc"]
[ext_resource type="PackedScene" uid="uid://cbu0ay7ryq6tu" path="res://scenes/enemy/quake/knight.tscn" id="2_etoth"]
[ext_resource type="PackedScene" uid="uid://tt2sjt4ypwn7" path="res://resource/entities/player/player_spawner.tscn" id="2_svnm2"]

[sub_resource type="PlaneMesh" id="PlaneMesh_svnm2"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_rsbl5"]

[sub_resource type="ConcavePolygonShape3D" id="ConcavePolygonShape3D_etoth"]
data = PackedVector3Array(1, 0, 1, -1, 0, 1, 1, 0, -1, -1, 0, 1, -1, 0, -1, 1, 0, -1)

[node name="Node3D" type="Node3D"]
script = ExtResource("1_f4dpc")
knight_scene = ExtResource("2_etoth")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
transform = Transform3D(12.6168, 0, 0, 0, 12.6168, 0, 0, 0, 12.6168, 0, 0, 0)
mesh = SubResource("PlaneMesh_svnm2")
surface_material_override/0 = SubResource("StandardMaterial3D_rsbl5")

[node name="StaticBody3D" type="StaticBody3D" parent="MeshInstance3D"]

[node name="CollisionShape3D" type="CollisionShape3D" parent="MeshInstance3D/StaticBody3D"]
shape = SubResource("ConcavePolygonShape3D_etoth")

[node name="PlayerSpawner" parent="." instance=ExtResource("2_svnm2")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.72634, 0)
