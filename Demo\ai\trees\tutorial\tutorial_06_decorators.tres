[gd_resource type="BehaviorTree" load_steps=34 format=3 uid="uid://beiki511huxb8"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_3xoj7"]
[ext_resource type="Script" uid="uid://b7v2utjmtge0x" path="res://demo/ai/tasks/in_range.gd" id="2_08b67"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="3_orhnl"]
[ext_resource type="Script" uid="uid://bicxffqmm7ek" path="res://demo/ai/tasks/select_random_nearby_pos.gd" id="4_oyght"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="5_5o1gy"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="6_rpn40"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_ewfwq"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BTAction" id="BTAction_2murg"]
script = ExtResource("1_3xoj7")
group = &"player"
output_var = &"target"

[sub_resource type="BTRunLimit" id="BTRunLimit_60b8b"]
children = [SubResource("BTAction_2murg")]

[sub_resource type="BTCondition" id="BTCondition_m15aa"]
script = ExtResource("2_08b67")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BTAction" id="BTAction_oc76s"]
script = ExtResource("3_orhnl")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_6d0yy"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_wsspf"]
await_completion = 5.0
animation_player = SubResource("BBNode_6d0yy")
animation_name = &"attack_1"

[sub_resource type="BTSequence" id="BTSequence_e0f8v"]
children = [SubResource("BTCondition_m15aa"), SubResource("BTAction_oc76s"), SubResource("BTPlayAnimation_wsspf")]

[sub_resource type="BTCooldown" id="BTCooldown_3tvjt"]
children = [SubResource("BTSequence_e0f8v")]
duration = 5.0

[sub_resource type="BTCondition" id="BTCondition_x0uu7"]
script = ExtResource("2_08b67")
distance_min = 0.0
distance_max = 200.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_icf24"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_iiei3"]
animation_player = SubResource("BBNode_icf24")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_824oc"]
script = ExtResource("4_oyght")
range_min = 700.0
range_max = 800.0
position_var = &"pos"

[sub_resource type="BTAction" id="BTAction_y1you"]
script = ExtResource("5_5o1gy")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BBNode" id="BBNode_ayt56"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_l1tdg"]
animation_player = SubResource("BBNode_ayt56")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_hh8ys"]
duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_cgwor"]
children = [SubResource("BTCondition_x0uu7"), SubResource("BTPlayAnimation_iiei3"), SubResource("BTAction_824oc"), SubResource("BTAction_y1you"), SubResource("BTPlayAnimation_l1tdg"), SubResource("BTWait_hh8ys")]

[sub_resource type="BTCondition" id="BTCondition_d6aub"]
script = ExtResource("2_08b67")
distance_min = 200.0
distance_max = 10000.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_rpwld"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_dug2k"]
animation_player = SubResource("BBNode_rpwld")
animation_name = &"walk"

[sub_resource type="BTAction" id="BTAction_wc11r"]
script = ExtResource("6_rpn40")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BBNode" id="BBNode_aw5jj"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_3aihc"]
animation_player = SubResource("BBNode_aw5jj")
animation_name = &"idle"

[sub_resource type="BTWait" id="BTWait_1o163"]
duration = 0.5

[sub_resource type="BTSequence" id="BTSequence_vx4uy"]
children = [SubResource("BTCondition_d6aub"), SubResource("BTPlayAnimation_dug2k"), SubResource("BTAction_wc11r"), SubResource("BTPlayAnimation_3aihc"), SubResource("BTWait_1o163")]

[sub_resource type="BTSelector" id="BTSelector_ddhoo"]
children = [SubResource("BTRunLimit_60b8b"), SubResource("BTCooldown_3tvjt"), SubResource("BTSequence_cgwor"), SubResource("BTSequence_vx4uy")]

[resource]
description = "Let's improve our previous behavior, and take the opportunity to discuss [dec]Decorators[/dec]. As mentioned earlier, decorators are control tasks that should only have a single child. Typically, a decorator either alters how its child operates or modifies its return status. They are often a useful tool for introducing variance into behaviors that otherwise lack it.

In our example, we utilize the [dec]RunLimit[/dec] decorator to restrict its child from running more than once. After executing the first time, the child will never receive another tick. This is quite handy for all sorts of setup logic!

Now, let's examine another decorator we use here - the [dec]Cooldown[/dec]. This decorator executes its child task only if the specified duration time has passed since the previous execution of the child task. The first time the attack sequence results in [SUCCESS], it triggers the cooldown, preventing the [comp]Sequence[/comp] from being executed for the next 5 seconds. During this period, the Cooldown returns [FAILURE] until the specified duration time elapses. It's important to note that it only counts executions that finished with [SUCCESS]. This means that [con]InRange[/con] can be utilized here without triggering the Cooldown!"
blackboard_plan = SubResource("BlackboardPlan_ewfwq")
root_task = SubResource("BTSelector_ddhoo")
