[gd_scene load_steps=8 format=3 uid="uid://d07ag5dcje13i"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_mswd4"]
[ext_resource type="Script" uid="uid://cgqdrwywolo6a" path="res://demo/agents/player/player.gd" id="2_24nyi"]
[ext_resource type="Script" uid="uid://cig7kaq6hm5nd" path="res://demo/agents/player/states/idle_state.gd" id="3_ekb12"]
[ext_resource type="Script" uid="uid://bu4henvxerree" path="res://demo/agents/player/states/move_state.gd" id="4_paikn"]
[ext_resource type="Script" uid="uid://csbtng53jffek" path="res://demo/agents/player/states/attack_state.gd" id="5_mpgu6"]
[ext_resource type="Script" uid="uid://di111ridlwuy5" path="res://demo/agents/player/states/dodge_state.gd" id="6_7o4a6"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_8pofm"]
size = Vector2(150, 50)

[node name="Player" groups=["player"] instance=ExtResource("1_mswd4")]
collision_layer = 0
collision_mask = 1
script = ExtResource("2_24nyi")
dodge_cooldown = 0.4

[node name="Hitbox" parent="Root" index="1"]
collision_mask = 8

[node name="HitboxCollisionShape2D" parent="Root/Hitbox" index="0"]
shape = SubResource("RectangleShape2D_8pofm")

[node name="Hurtbox" parent="Root" index="2"]
collision_layer = 4

[node name="Health" parent="." index="3"]
max_health = 30.0

[node name="LimboHSM" type="LimboHSM" parent="." index="4"]

[node name="IdleState" type="LimboState" parent="LimboHSM" index="0" node_paths=PackedStringArray("animation_player")]
script = ExtResource("3_ekb12")
animation_player = NodePath("../../AnimationPlayer")
idle_animation = &"idle"

[node name="MoveState" type="LimboState" parent="LimboHSM" index="1" node_paths=PackedStringArray("animation_player")]
script = ExtResource("4_paikn")
animation_player = NodePath("../../AnimationPlayer")
animation = &"walk"

[node name="AttackState" type="LimboState" parent="LimboHSM" index="2" node_paths=PackedStringArray("animation_player", "hitbox")]
script = ExtResource("5_mpgu6")
animation_player = NodePath("../../AnimationPlayer")
animations = Array[StringName]([&"attack_1", &"attack_2", &"attack_3"])
hitbox = NodePath("../../Root/Hitbox")

[node name="DodgeState" type="LimboState" parent="LimboHSM" index="3" node_paths=PackedStringArray("animation_player", "hurtbox_collision")]
script = ExtResource("6_7o4a6")
animation_player = NodePath("../../AnimationPlayer")
animation = &"dodge"
hurtbox_collision = NodePath("../../Root/Hurtbox/HurtboxCollisionShape2D")
