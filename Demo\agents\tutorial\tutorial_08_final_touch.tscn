[gd_scene load_steps=5 format=3 uid="uid://b8yj40s43bw8h"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_bjdsc"]
[ext_resource type="Texture2D" uid="uid://b0oeqsc0xksto" path="res://demo/assets/agent_junior_pieces.png" id="2_onjfd"]
[ext_resource type="BehaviorTree" uid="uid://dp0cglcytwcj5" path="res://demo/ai/trees/tutorial/tutorial_08_final_touch.tres" id="3_c5qx4"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_52mdk"]

[node name="TutorialFinalTouch" instance=ExtResource("1_bjdsc")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_onjfd")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_onjfd")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_onjfd")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_onjfd")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_onjfd")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_onjfd")

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_c5qx4")
blackboard_plan = SubResource("BlackboardPlan_52mdk")
