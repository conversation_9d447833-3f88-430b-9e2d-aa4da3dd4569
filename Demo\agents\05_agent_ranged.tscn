[gd_scene load_steps=5 format=3 uid="uid://ci3x5esepbu5v"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_oa4xm"]
[ext_resource type="Texture2D" uid="uid://cjts4ennjtepg" path="res://demo/assets/agent_ranged.png" id="2_37lvn"]
[ext_resource type="BehaviorTree" uid="uid://cqluon1y1hnn5" path="res://demo/ai/trees/05_agent_ranged.tres" id="3_f7r5w"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_2kwy7"]

[node name="AgentRanged" instance=ExtResource("1_oa4xm")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_37lvn")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_37lvn")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_37lvn")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_37lvn")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_37lvn")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_37lvn")

[node name="Health" parent="." index="3"]
max_health = 6.0

[node name="BTPlayer" type="BTPlayer" parent="." index="4"]
behavior_tree = ExtResource("3_f7r5w")
blackboard_plan = SubResource("BlackboardPlan_2kwy7")
