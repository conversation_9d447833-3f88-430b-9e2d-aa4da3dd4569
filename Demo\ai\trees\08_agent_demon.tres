[gd_resource type="BehaviorTree" load_steps=49 format=3 uid="uid://cpcnbi81jqge4"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_ce4la"]
[ext_resource type="Script" uid="uid://b7v2utjmtge0x" path="res://demo/ai/tasks/in_range.gd" id="2_atyuj"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="3_3mw7l"]
[ext_resource type="Script" uid="uid://bi5e8366xi5s5" path="res://demo/ai/tasks/back_away.gd" id="4_6dr32"]
[ext_resource type="Script" uid="uid://ct71h72pech3b" path="res://demo/ai/tasks/select_flanking_pos.gd" id="5_dho0d"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="6_0pfsl"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_8ay3j"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"

[sub_resource type="BBNode" id="BBNode_lpri5"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_p3e7a"]
animation_player = SubResource("BBNode_lpri5")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_j6cjm"]
min_duration = 2.0
max_duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_5thv4"]
custom_name = "Chill"
children = [SubResource("BTPlayAnimation_p3e7a"), SubResource("BTRandomWait_j6cjm")]

[sub_resource type="BTCooldown" id="BTCooldown_oms7a"]
children = [SubResource("BTSequence_5thv4")]
trigger_on_failure = true

[sub_resource type="BTAction" id="BTAction_ohfp7"]
script = ExtResource("1_ce4la")
group = &"player"
output_var = &"target"

[sub_resource type="BTCondition" id="BTCondition_7a5nv"]
script = ExtResource("2_atyuj")
distance_min = 0.0
distance_max = 150.0
target_var = &"target"

[sub_resource type="BTAction" id="BTAction_ddvrs"]
script = ExtResource("3_3mw7l")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_c4nfu"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_yytkn"]
await_completion = 3.0
animation_player = SubResource("BBNode_c4nfu")
animation_name = &"attack_3"
blend = 0.1

[sub_resource type="BTCooldown" id="BTCooldown_ejg6q"]
children = [SubResource("BTPlayAnimation_yytkn")]
duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_eikr4"]
custom_name = "Melee"
children = [SubResource("BTAction_ohfp7"), SubResource("BTCondition_7a5nv"), SubResource("BTAction_ddvrs"), SubResource("BTCooldown_ejg6q")]

[sub_resource type="BTCondition" id="BTCondition_h75v8"]
script = ExtResource("2_atyuj")
distance_min = 0.0
distance_max = 300.0
target_var = &"target"

[sub_resource type="BTAction" id="BTAction_wpt7j"]
script = ExtResource("3_3mw7l")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_iv62h"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_wnaul"]
animation_player = SubResource("BBNode_iv62h")
animation_name = &"walk"
blend = 0.1
speed = -1.0

[sub_resource type="BTAction" id="BTAction_h2efl"]
script = ExtResource("4_6dr32")
speed_var = &"speed"
max_angle_deviation = 0.7

[sub_resource type="BTTimeLimit" id="BTTimeLimit_wm5g2"]
children = [SubResource("BTAction_h2efl")]
time_limit = 1.0

[sub_resource type="BTSequence" id="BTSequence_wh4dl"]
custom_name = "Back away"
children = [SubResource("BTCondition_h75v8"), SubResource("BTAction_wpt7j"), SubResource("BTPlayAnimation_wnaul"), SubResource("BTTimeLimit_wm5g2")]

[sub_resource type="BTComment" id="BTComment_mqte5"]
custom_name = "Get into position"

[sub_resource type="BBNode" id="BBNode_edmui"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_vjtpq"]
animation_player = SubResource("BBNode_edmui")
animation_name = &"walk"
blend = 0.1

[sub_resource type="BTAction" id="BTAction_4mmh0"]
script = ExtResource("5_dho0d")
target_var = &"target"
flank_side = 0
range_min = 300
range_max = 700
position_var = &"pos"

[sub_resource type="BTAction" id="BTAction_vb5c3"]
script = ExtResource("6_0pfsl")
target_position_var = &"pos"
speed_var = &"speed"
tolerance = 50.0
avoid_var = &""

[sub_resource type="BTTimeLimit" id="BTTimeLimit_jyks2"]
children = [SubResource("BTAction_vb5c3")]

[sub_resource type="BTComment" id="BTComment_ym6nj"]
custom_name = "Short break before action"

[sub_resource type="BTAction" id="BTAction_fkevy"]
script = ExtResource("3_3mw7l")
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_lh25u"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_vcjeq"]
animation_player = SubResource("BBNode_lh25u")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTWait" id="BTWait_42smh"]
duration = 0.2

[sub_resource type="BTComment" id="BTComment_nbp2b"]
custom_name = "Spit fire!"

[sub_resource type="BBNode" id="BBNode_cqw71"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_214yn"]
await_completion = 3.0
animation_player = SubResource("BBNode_cqw71")
animation_name = &"spit"
blend = 0.1

[sub_resource type="BBNode" id="BBNode_dv5b5"]
resource_name = "."
saved_value = NodePath(".")

[sub_resource type="BTCallMethod" id="BTCallMethod_lk7i6"]
node = SubResource("BBNode_dv5b5")
method = &"spit_fire"

[sub_resource type="BTComment" id="BTComment_fndxk"]
custom_name = "Wind down"

[sub_resource type="BBNode" id="BBNode_m68ui"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_m7gyp"]
animation_player = SubResource("BBNode_m68ui")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_v0el8"]
min_duration = 0.7
max_duration = 1.5

[sub_resource type="BTSequence" id="BTSequence_djtph"]
custom_name = "Spit fire"
children = [SubResource("BTComment_mqte5"), SubResource("BTPlayAnimation_vjtpq"), SubResource("BTAction_4mmh0"), SubResource("BTTimeLimit_jyks2"), SubResource("BTComment_ym6nj"), SubResource("BTAction_fkevy"), SubResource("BTPlayAnimation_vcjeq"), SubResource("BTWait_42smh"), SubResource("BTComment_nbp2b"), SubResource("BTPlayAnimation_214yn"), SubResource("BTCallMethod_lk7i6"), SubResource("BTComment_fndxk"), SubResource("BTPlayAnimation_m7gyp"), SubResource("BTRandomWait_v0el8")]

[sub_resource type="BTSelector" id="BTSelector_feba6"]
children = [SubResource("BTCooldown_oms7a"), SubResource("BTSequence_eikr4"), SubResource("BTSequence_wh4dl"), SubResource("BTSequence_djtph")]

[resource]
description = "Here, we use the [con]InRange[/con] condition task together with [comp]Sequence[/comp] and [comp]Selector[/comp] to create a reactive behavior for this agent. When the player comes close, this agent will attempt to hit them with a melee attack and move away afterward for a better ranged position."
blackboard_plan = SubResource("BlackboardPlan_8ay3j")
root_task = SubResource("BTSelector_feba6")
