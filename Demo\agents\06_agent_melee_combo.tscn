[gd_scene load_steps=5 format=3 uid="uid://blxmw2w1h2s0s"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_dny3b"]
[ext_resource type="Texture2D" uid="uid://bo0ibp7tvjbba" path="res://demo/assets/agent_combo.png" id="2_4rqld"]
[ext_resource type="BehaviorTree" uid="uid://cpncl1db8j12f" path="res://demo/ai/trees/06_agent_melee_combo.tres" id="3_l805q"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_67ric"]

[node name="AgentMeleeCombo" instance=ExtResource("1_dny3b")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_4rqld")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_4rqld")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_4rqld")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_4rqld")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_4rqld")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_4rqld")

[node name="Health" parent="." index="3"]
max_health = 6.0

[node name="BTPlayer" type="BTPlayer" parent="." index="4"]
behavior_tree = ExtResource("3_l805q")
blackboard_plan = SubResource("BlackboardPlan_67ric")
