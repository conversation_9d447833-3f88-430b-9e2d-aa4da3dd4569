[gd_resource type="BehaviorTree" load_steps=44 format=3 uid="uid://qqmjvbeibatn"]

[ext_resource type="Script" uid="uid://ccr43pgd4488l" path="res://demo/ai/tasks/get_first_in_group.gd" id="1_2883n"]
[ext_resource type="Script" uid="uid://ct71h72pech3b" path="res://demo/ai/tasks/select_flanking_pos.gd" id="2_cjso2"]
[ext_resource type="Script" uid="uid://dcjgktglb1slf" path="res://demo/ai/tasks/pursue.gd" id="2_lpckh"]
[ext_resource type="Script" uid="uid://df82exuqnfdb2" path="res://demo/ai/tasks/arrive_pos.gd" id="3_treio"]
[ext_resource type="Script" uid="uid://dbo0kq2cwb4qv" path="res://demo/ai/tasks/face_target.gd" id="4_57x51"]
[ext_resource type="Script" uid="uid://b7v2utjmtge0x" path="res://demo/ai/tasks/in_range.gd" id="5_p5dih"]
[ext_resource type="Script" uid="uid://bi5e8366xi5s5" path="res://demo/ai/tasks/back_away.gd" id="6_fkv0k"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_46tbn"]
var/speed/name = &"speed"
var/speed/type = 3
var/speed/value = 400.0
var/speed/hint = 1
var/speed/hint_string = "10,1000,10"
var/fast_speed/name = &"fast_speed"
var/fast_speed/type = 3
var/fast_speed/value = 600.0
var/fast_speed/hint = 1
var/fast_speed/hint_string = "10,1000,10"
var/slow_speed/name = &"slow_speed"
var/slow_speed/type = 3
var/slow_speed/value = 300.0
var/slow_speed/hint = 1
var/slow_speed/hint_string = "10,1000,10"

[sub_resource type="BTAction" id="BTAction_ulbrf"]
script = ExtResource("1_2883n")
group = &"player"
output_var = &"target"

[sub_resource type="BBNode" id="BBNode_nrd4b"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_qiw21"]
animation_player = SubResource("BBNode_nrd4b")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_xlud8"]
min_duration = 2.0
max_duration = 3.0

[sub_resource type="BTSequence" id="BTSequence_yhjh1"]
custom_name = "Summoning sickness"
children = [SubResource("BTAction_ulbrf"), SubResource("BTPlayAnimation_qiw21"), SubResource("BTRandomWait_xlud8")]

[sub_resource type="BTRunLimit" id="BTRunLimit_tq3e1"]
children = [SubResource("BTSequence_yhjh1")]

[sub_resource type="BTCondition" id="BTCondition_uk4dg"]
script = ExtResource("5_p5dih")
distance_min = 0.0
distance_max = 300.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_7c0g0"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_sty80"]
animation_player = SubResource("BBNode_7c0g0")
animation_name = &"walk"
blend = 0.1
speed = 1.2

[sub_resource type="BTAction" id="BTAction_jryg6"]
script = ExtResource("2_lpckh")
target_var = &"target"
speed_var = &"speed"
approach_distance = 100.0

[sub_resource type="BTTimeLimit" id="BTTimeLimit_lkphr"]
children = [SubResource("BTAction_jryg6")]
time_limit = 1.0

[sub_resource type="BTAction" id="BTAction_kidxn"]
script = ExtResource("4_57x51")
target_var = &"target"

[sub_resource type="BTWait" id="BTWait_tadkc"]
duration = 0.2

[sub_resource type="BBNode" id="BBNode_kcqly"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_hngi6"]
await_completion = 2.0
animation_player = SubResource("BBNode_kcqly")
animation_name = &"attack_3"

[sub_resource type="BTWait" id="BTWait_cm8jy"]
duration = 0.5

[sub_resource type="BTSequence" id="BTSequence_ww5v2"]
custom_name = "Melee attack"
children = [SubResource("BTCondition_uk4dg"), SubResource("BTPlayAnimation_sty80"), SubResource("BTTimeLimit_lkphr"), SubResource("BTAction_kidxn"), SubResource("BTWait_tadkc"), SubResource("BTPlayAnimation_hngi6"), SubResource("BTWait_cm8jy")]

[sub_resource type="BTCooldown" id="BTCooldown_ksvfy"]
children = [SubResource("BTSequence_ww5v2")]
duration = 2.0

[sub_resource type="BTCondition" id="BTCondition_fpufi"]
script = ExtResource("5_p5dih")
distance_min = 0.0
distance_max = 300.0
target_var = &"target"

[sub_resource type="BBNode" id="BBNode_3iqcf"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_ee0ff"]
animation_player = SubResource("BBNode_3iqcf")
animation_name = &"walk"
blend = 0.1
speed = -0.7

[sub_resource type="BTAction" id="BTAction_4ye2y"]
script = ExtResource("6_fkv0k")
speed_var = &"slow_speed"
max_angle_deviation = 0.7

[sub_resource type="BTTimeLimit" id="BTTimeLimit_cns1i"]
children = [SubResource("BTAction_4ye2y")]
time_limit = 1.5

[sub_resource type="BTAlwaysSucceed" id="BTAlwaysSucceed_nw4ep"]
children = [SubResource("BTTimeLimit_cns1i")]

[sub_resource type="BBNode" id="BBNode_81x7t"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_e61er"]
animation_player = SubResource("BBNode_81x7t")
animation_name = &"idle"
blend = 0.1

[sub_resource type="BTRandomWait" id="BTRandomWait_jw0cm"]

[sub_resource type="BTSequence" id="BTSequence_y12eg"]
custom_name = "Disengage"
children = [SubResource("BTCondition_fpufi"), SubResource("BTPlayAnimation_ee0ff"), SubResource("BTAlwaysSucceed_nw4ep"), SubResource("BTPlayAnimation_e61er"), SubResource("BTRandomWait_jw0cm")]

[sub_resource type="BBNode" id="BBNode_wpj6d"]
resource_name = "AnimationPlayer"
saved_value = NodePath("AnimationPlayer")

[sub_resource type="BTPlayAnimation" id="BTPlayAnimation_olf37"]
animation_player = SubResource("BBNode_wpj6d")
animation_name = &"walk"
blend = 0.1
speed = 1.2

[sub_resource type="BTAction" id="BTAction_g5ayy"]
script = ExtResource("2_cjso2")
target_var = &"target"
flank_side = 2
range_min = 90
range_max = 90
position_var = &"flank_pos"

[sub_resource type="BTAction" id="BTAction_tv4lt"]
script = ExtResource("3_treio")
target_position_var = &"flank_pos"
speed_var = &"fast_speed"
tolerance = 50.0
avoid_var = &"target"

[sub_resource type="BTTimeLimit" id="BTTimeLimit_xek5v"]
children = [SubResource("BTAction_tv4lt")]

[sub_resource type="BTSequence" id="BTSequence_rgwic"]
custom_name = "Flank from behind"
children = [SubResource("BTPlayAnimation_olf37"), SubResource("BTAction_g5ayy"), SubResource("BTTimeLimit_xek5v")]

[sub_resource type="BTSelector" id="BTSelector_2jnau"]
children = [SubResource("BTRunLimit_tq3e1"), SubResource("BTCooldown_ksvfy"), SubResource("BTSequence_y12eg"), SubResource("BTSequence_rgwic")]

[resource]
description = "Like actions, conditions do not contain child tasks within them. Instead, they evaluate specific criteria and return either a [SUCCESS] or [FAILURE] based on the agent's or environment's state (for example, \"IsLowOnHealth\", \"IsTargetInSight\"). Conditions are commonly employed alongside sequences to interrupt their execution if a particular requirement is not met.

In our example, if the target is not within the specified range, the [con]InRange[/con] condition will return [FAILURE], causing the [comp]sequence[/comp] to be aborted.
"
blackboard_plan = SubResource("BlackboardPlan_46tbn")
root_task = SubResource("BTSelector_2jnau")
