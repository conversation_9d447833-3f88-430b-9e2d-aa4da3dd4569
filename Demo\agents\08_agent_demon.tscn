[gd_scene load_steps=5 format=3 uid="uid://dbmcufef0cc4b"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_115kp"]
[ext_resource type="Texture2D" uid="uid://d1tx7u8ho0r4v" path="res://demo/assets/agent_demon.png" id="2_cb5f2"]
[ext_resource type="BehaviorTree" uid="uid://cpcnbi81jqge4" path="res://demo/ai/trees/08_agent_demon.tres" id="3_ebd57"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_5sr4g"]

[node name="AgentDemon" instance=ExtResource("1_115kp")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_cb5f2")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_cb5f2")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_cb5f2")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_cb5f2")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_cb5f2")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_cb5f2")

[node name="Health" parent="." index="3"]
max_health = 8.0

[node name="BTPlayer" type="BTPlayer" parent="." index="5"]
behavior_tree = ExtResource("3_ebd57")
blackboard_plan = SubResource("BlackboardPlan_5sr4g")
