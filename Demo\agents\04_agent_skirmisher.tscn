[gd_scene load_steps=5 format=3 uid="uid://co6yeafaljbq0"]

[ext_resource type="PackedScene" uid="uid://ooigbfhfy4wa" path="res://demo/agents/agent_base.tscn" id="1_2ir76"]
[ext_resource type="Texture2D" uid="uid://l042ovqqsy3l" path="res://demo/assets/agent_skirmisher.png" id="2_w8tqw"]
[ext_resource type="BehaviorTree" uid="uid://qqmjvbeibatn" path="res://demo/ai/trees/04_agent_skirmisher.tres" id="3_bhfkv"]

[sub_resource type="BlackboardPlan" id="BlackboardPlan_vjbry"]

[node name="AgentSkirmisher" instance=ExtResource("1_2ir76")]

[node name="LegL" parent="Root/Rig" index="1"]
texture = ExtResource("2_w8tqw")

[node name="LegR" parent="Root/Rig" index="2"]
texture = ExtResource("2_w8tqw")

[node name="Body" parent="Root/Rig" index="3"]
texture = ExtResource("2_w8tqw")

[node name="Hat" parent="Root/Rig/Body" index="0"]
texture = ExtResource("2_w8tqw")

[node name="HandL" parent="Root/Rig/Body" index="1"]
texture = ExtResource("2_w8tqw")

[node name="HandR" parent="Root/Rig/Body" index="2"]
texture = ExtResource("2_w8tqw")

[node name="Health" parent="." index="3"]
max_health = 7.0

[node name="BTPlayer" type="BTPlayer" parent="." index="4"]
behavior_tree = ExtResource("3_bhfkv")
blackboard_plan = SubResource("BlackboardPlan_vjbry")
